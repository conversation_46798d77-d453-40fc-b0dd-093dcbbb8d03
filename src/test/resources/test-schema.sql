-- TestContainers 测试数据库初始化脚本
-- 基于项目现有的数据库结构

-- 用户表
CREATE TABLE `user` (
  `id` int(11) NOT NULL,
  `type` int(1) NOT NULL DEFAULT '0' COMMENT '账户类型: 0-普通账户 1-钱包账户',
  `address` varchar(42) DEFAULT NULL COMMENT '地址',
  `code` varchar(32) DEFAULT NULL COMMENT '我的邀请码',
  `pid` int(32) DEFAULT NULL COMMENT '邀请人ID',
  `level` int(11) NOT NULL DEFAULT '1' COMMENT '等级',
  `pre_level` int(11) NOT NULL DEFAULT '1' COMMENT '预售等级',
  `lock_level` tinyint(1) DEFAULT '0' NOT NULL COMMENT '是否锁定等级',
  `set_level_time` datetime DEFAULT NULL COMMENT '设置等级时间',
  `quadruple_reward_times` int(11) NOT NULL DEFAULT '5' COMMENT '4倍收益次数',
  `juid` varchar(255) NULL COMMENT '交易所ID',
  `last_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后登录时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';


-- auto-generated definition
create table stake_user
(
    id             int auto_increment
        primary key,
    user_id        int                                      not null comment '用户ID',
    token_id       varchar(32)                              not null comment '资产ID',
    current_amount decimal(25, 8) default 0.00000000        not null comment '活期限额',
    pending_amount decimal(25, 8) default 0.00000000        not null comment '待确认',
    static_pool    decimal(25, 8) default 0.00000000        not null comment '静态池子',
    dynamic_pool   decimal(25, 8) default 0.00000000        not null comment '动态池子',
    today_static   decimal(25, 8) default 0.00000000        not null comment '今日静态收益',
    total_static   decimal(25, 8) default 0.00000000        not null comment '累计静态收益',
    today_dynamic  decimal(25, 8) default 0.00000000        not null comment '今日动态收益',
    total_dynamic  decimal(25, 8) default 0.00000000        not null comment '累计动态收益',
    today_buy      decimal(25, 8) default 0.00000000        not null comment '今日购买',
    total_buy      decimal(25, 8) default 0.00000000        not null comment '累计购买',
    can_receive    decimal(25, 8) default 0.00000000        not null comment '可领取',
    week_dynamic   decimal(25, 8) default 0.00000000        not null comment '周分红',
    team_perf      decimal(25, 8) default 0.00000000        not null comment '团队业绩',
    old_team_perf  decimal(25, 8) default 0.00000000        not null comment '上周团队业绩',
    max_team_perf  decimal(25, 8) default 0.00000000        not null comment '历史最大团队业绩',
    node           int            default 0                 not null comment '节点数量',
    node_perf      decimal(25, 8) default 0.00000000        not null comment '节点业绩',
    max_node_perf  decimal(25, 8) default 0.00000000        not null comment '历史最大节点业绩',
    node_reward    decimal(25, 8) default 0.00000000        not null comment '节点返佣',
    node_pool      decimal(25, 8) default 0.00000000        not null comment '节点池',
    sum_amount     decimal(25, 8) default 0.00000000        not null comment '伞下业绩',
    stake_first    tinyint(1)     default 0                 not null comment '是否完成首次质押',
    stake_limit    decimal(25, 8) default 0.00000000        not null comment '质押额度',
    burn_limit     decimal(25, 8) default 0.00000000        not null comment '销毁额度',
    create_time    datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time    datetime       default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
)
    comment '质押用户表';

-- 用户质押记录表
CREATE TABLE `user_stake` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) NOT NULL COMMENT '类型：0-质押 1-解除质押',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `token_id` varchar(32) NOT NULL COMMENT 'TOKEN',
  `quantity` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '数量',
  `txid` varchar(66) DEFAULT NULL COMMENT '交易号',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` bigint(1) NOT NULL DEFAULT '0' COMMENT '0-待确认 1-有效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户质押记录';

-- 系统配置表
CREATE TABLE `sys_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reward_rate` decimal(25,8) NOT NULL COMMENT '金额',
  `times` json DEFAULT NULL COMMENT '时间段配置',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 用户关系表
CREATE TABLE `user_relation` (
  `id` int(11) NOT NULL,
  `pid` int(11) DEFAULT NULL,
  `layer` int(11) NOT NULL DEFAULT '0',
  `path` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 资产表
CREATE TABLE `asset` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token_id` varchar(32) NOT NULL COMMENT '资产ID',
  `token_name` varchar(32) NOT NULL COMMENT '资产名称',
  `logo` varchar(512) DEFAULT NULL,
  `ido_address` varchar(64) DEFAULT NULL,
  `stake_address` varchar(64) DEFAULT NULL,
  `token_address` varchar(64) DEFAULT NULL,
  `stake` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可以质押',
  `total_staked` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '总质押',
  `total_supply` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '回购池',
  `total_burn` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '销毁池',
  `total_dao` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT 'dao池',
  `version` int(13) NOT NULL DEFAULT '0' COMMENT '版本',
  `total_week_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '周分红池',
  `real_week_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '周分红池(实发)',
  `node_pool` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '节点分红',
  `total_supply_bnb` decimal(25,8) NOT NULL DEFAULT '0.00000000' COMMENT '回购池bnb',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资产表';

-- 质押奖励日志表
CREATE TABLE `stake_reward_log` (
    `id`             int auto_increment primary key,
    `user_id`        int                       not null comment '用户ID',
    `child_user_id`  int                       not null comment '下级用户ID',
    `level`          int                       not null comment '层级',
    `product_amount` decimal(25, 8) default 0.00000000 not null comment '质押数量',
    `amount`         decimal(25, 8)            not null comment '返佣数量',
    `create_time`    datetime                  not null comment '创建时间',
    `symbol`         varchar(32)    default 'FIST'     null comment '币种',
    `product_id`     int                       null comment '用户下单ID',
    `pay_method`     varchar(32)               null comment '支付方式'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质押奖励日志';

-- 管理员用户表
CREATE TABLE `sys_user` (
    `id`          int not null auto_increment,
    `account`     text,
    `password`    text,
    `name`        text,
    `login_check` text,
    `state`       int         DEFAULT NULL,
    `create_date` datetime    DEFAULT NULL,
    primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

-- 插入测试数据
-- 1. 系统配置
INSERT INTO `sys_config` (`id`, `reward_rate`, `times`) 
VALUES (1, 0.01, '["0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004","0.004"]');

-- 2. 资产配置
INSERT INTO `asset`(`id`, `token_id`, `token_name`, `stake`, `stake_address`, `token_address`)
VALUES (1, 'FIST', 'FIST', 1, '******************************************', '0x8a6556FaA0846d329D470Ce1342236ca2c6609d0');

-- 3. 管理员用户
INSERT INTO `sys_user`(id, account, password, name, login_check, state, create_date)
VALUES (1, 'admin', 'ade8110ecb08952f9667d36b5aa63307', 'admin', 'PasswordImgCodeCheck', 0, now());


drop table if exists user_product;
create table user_product
(
    id      int              not null auto_increment,
    user_id varchar(32) not null comment '用户ID',
    type int(1) not null comment '类型 0-活期 1-定期',
    order_no varchar(32) not null comment '订单号',
    product_id int(1) not null comment '产品ID',
    power decimal(25,8) not null default 0 comment '释放金额',
    amount decimal(25,8) not null default 0 comment '申请金额',
    profit decimal(25,8) not null default 0 comment '利息',
    fee decimal(25,8) not null default 0 comment '手续费',
    rate decimal(25,6) not null comment '日收益率',
    day int(11) not null comment '天数',
    release_day int(11) not null comment '发放天数',
    create_time datetime not null comment '创建时间',
    status bigint(1) not null default 0 comment '0-待确认 1-有效，2-已经期',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    constraint id
        primary key (id)
)
    comment '用户订单表';

alter table user_product add column quantity int(11) not null default 0 comment '数量' after user_id;
alter table user_product add column price decimal(25,8) not null default 0 comment '价格' after quantity;
alter table user_product add column daily_release_amount decimal(25,8) default 0 comment '每日释放金额';
alter table user_product add column total_released decimal(25,8) default 0 comment '已释放总金额';
alter table user_product add column available_amount decimal(25,8) default 0 comment '可提取金额(已释放未提取)';
alter table user_product add column last_release_date date comment '最后释放日期';

create table user_log
(
    id      int              not null auto_increment,
    user_id int(32) not null comment '用户ID',
    type int(2) not null comment '类型',
    product_amount decimal(25,8) not null comment '理财金额',
    amount decimal(25,8) not null comment '金额',
    last_amount decimal(25,8) not null default 0 comment '金额',
    remark varchar(128) null comment '备注',
    symbol varchar(32) DEFAULT 'FTST' null comment '币种',
    token_id varchar(32) null comment 'TOKEN',
    create_time datetime not null comment '创建时间',
    primary key (id)
)
    comment '用户流水';

-- 锁仓订单表
CREATE TABLE `lock_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` VARCHAR(64) NOT NULL COMMENT '锁仓订单号',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `source_type` TINYINT NOT NULL COMMENT '来源类型: 1-节点产品 2-普通商品 3-债券产品 4-质押 5-其他',
    `source_id` BIGINT NULL COMMENT '来源ID(user_product.id或其他业务ID)',
    `token_id` VARCHAR(32) NOT NULL COMMENT '代币ID',
    
    -- 锁仓基本信息
    `lock_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '锁仓总金额',
    `usd_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT 'USD金额(购买时的美元金额)',
    `released_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '已释放金额',
    `available_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '可提取金额(已释放未提取)',
    
    -- 释放规则
    `total_days` INT NOT NULL COMMENT '总锁仓天数',
    `released_days` INT NOT NULL DEFAULT 0 COMMENT '已释放天数',
    `daily_release_amount` DECIMAL(25,8) NOT NULL DEFAULT 0 COMMENT '每日释放金额',
    
    -- 状态和释放计数
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0-待激活 1-锁仓中 2-已完成 3-已取消',
    
    -- 扩展配置
    `lock_config` JSON NULL COMMENT '锁仓配置(阶梯释放规则等)',
    
    -- 审计字段
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_token_id` (`token_id`),
    KEY `idx_source` (`source_type`, `source_id`),
    KEY `idx_released_days` (`released_days`),
    KEY `idx_lock_order_usd_amount` (`usd_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='锁仓订单表';