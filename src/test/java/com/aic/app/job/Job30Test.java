package com.aic.app.job;

import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserMapper;
import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.mapper.LockOrderMapper;
import com.aic.app.mapper.UserLogMapper;
import com.aic.app.model.*;
import com.aic.app.service.ISysConfigService;
import com.aic.app.service.IUserLogService;
import com.aic.app.service.ILockOrderService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(locations = "classpath:application-testcontainers.properties")
@Transactional
@DisplayName("Job30 质押收益集成测试")
public class Job30Test {

    @Autowired
    private Job30 job30;

    @Autowired
    private StakeUserMapper stakeUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRelationMapper userRelationMapper;

    @Autowired
    private LockOrderMapper lockOrderMapper;

    @Autowired
    private UserLogMapper userLogMapper;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IUserLogService userLogService;

    @Autowired
    private ILockOrderService lockOrderService;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        cleanTestData();
        
        // 确保系统配置存在
        ensureSystemConfig();
    }

    private void cleanTestData() {
        // 清理测试相关数据
        stakeUserMapper.delete(new LambdaQueryWrapper<StakeUser>().ge(StakeUser::getUserId, 1000L));
        userMapper.delete(new LambdaQueryWrapper<User>().ge(User::getId, 1000L));
        userRelationMapper.delete(new LambdaQueryWrapper<UserRelation>().ge(UserRelation::getId, 1000L));
        lockOrderMapper.delete(new LambdaQueryWrapper<LockOrder>().ge(LockOrder::getUserId, 1000L));
        userLogMapper.delete(new LambdaQueryWrapper<UserLog>().ge(UserLog::getUserId, 1000L));
    }

    private void ensureSystemConfig() {
        // 确保系统配置存在，如果不存在则创建
        SysConfig config = sysConfigService.getSysConfig();
        if (config == null || config.getRewardRate() == null) {
            // 这里可能需要根据实际的SysConfig创建方式来调整
            // 暂时跳过，假设配置已存在
        }
    }

    // 辅助方法：创建测试用户
    private User createTestUser(Long userId, int level, int quadrupleRewardTimes) {
        User user = new User();
        user.setId(userId);
        user.setLevel(level);
        user.setQuadrupleRewardTimes(quadrupleRewardTimes);
        user.setType(1);
        user.setAddress("test_address_" + userId);
        user.setCode("test_code_" + userId);
        user.setCreateTime(new Date());
        userMapper.insert(user);
        return user;
    }

    // 辅助方法：创建测试质押用户
    private StakeUser createTestStakeUser(Long userId, BigDecimal currentAmount, 
                                         BigDecimal pendingAmount, BigDecimal staticPool, 
                                         BigDecimal dynamicPool) {
        StakeUser stakeUser = new StakeUser();
        stakeUser.setUserId(userId);
        stakeUser.setTokenId("XYC");
        stakeUser.setCurrentAmount(currentAmount);
        stakeUser.setPendingAmount(pendingAmount);
        stakeUser.setStaticPool(staticPool);
        stakeUser.setDynamicPool(dynamicPool);
        stakeUser.setLockStaticPool(BigDecimal.ZERO);
        stakeUser.setLockDynamicPool(BigDecimal.ZERO);
        stakeUser.setTodayStatic(BigDecimal.ZERO);
        stakeUser.setTotalStatic(BigDecimal.ZERO);
        stakeUser.setTodayDynamic(BigDecimal.ZERO);
        stakeUser.setTotalDynamic(BigDecimal.ZERO);
        stakeUser.setCreateTime(new Date());
        stakeUserMapper.insert(stakeUser);
        return stakeUser;
    }

    // 辅助方法：创建用户关系
    private UserRelation createUserRelation(Long userId, Long pid, String path) {
        UserRelation relation = new UserRelation();
        relation.setId(userId);
        relation.setPid(pid);
        relation.setPath(path);
        relation.setLayer(path.split("/").length - 1);
        userRelationMapper.insert(relation);
        return relation;
    }

    // 辅助方法：创建锁仓订单
    private LockOrder createTestLockOrder(Long userId, BigDecimal lockAmount, 
                                         BigDecimal releasedAmount, BigDecimal availableAmount,
                                         int totalDays, int releasedDays) {
        LockOrder lockOrder = new LockOrder();
        lockOrder.setOrderNo("TEST_ORDER_" + userId + "_" + System.currentTimeMillis());
        lockOrder.setUserId(userId);
        lockOrder.setSourceType(2); // 普通商品
        lockOrder.setTokenId("XYC");
        lockOrder.setLockAmount(lockAmount);
        lockOrder.setUsdAmount(lockAmount); // 设置 USD 金额等于锁仓金额
        lockOrder.setReleasedAmount(releasedAmount);
        lockOrder.setAvailableAmount(availableAmount);
        lockOrder.setTotalDays(totalDays);
        lockOrder.setReleasedDays(releasedDays);
        lockOrder.setDailyReleaseAmount(lockAmount.divide(new BigDecimal(totalDays), 8, BigDecimal.ROUND_HALF_UP));
        lockOrder.setStatus(1); // 锁仓中
        lockOrder.setCreateTime(new Date());
        lockOrderMapper.insert(lockOrder);
        return lockOrder;
    }
    
    // 辅助方法：创建锁仓订单（用于等级升级测试）
    private LockOrder createTestLockOrderForUpgrade(Long userId, BigDecimal usdAmount) {
        LockOrder lockOrder = new LockOrder();
        lockOrder.setOrderNo("UPGRADE_ORDER_" + userId + "_" + System.currentTimeMillis());
        lockOrder.setUserId(userId);
        lockOrder.setSourceType(3); // 债券产品
        lockOrder.setTokenId("XYC");
        lockOrder.setLockAmount(usdAmount.divide(new BigDecimal("10"), 8, BigDecimal.ROUND_HALF_UP)); // XYC金额
        lockOrder.setUsdAmount(usdAmount); // USD 金额
        lockOrder.setReleasedAmount(BigDecimal.ZERO);
        lockOrder.setAvailableAmount(BigDecimal.ZERO);
        lockOrder.setTotalDays(360);
        lockOrder.setReleasedDays(0);
        lockOrder.setDailyReleaseAmount(lockOrder.getLockAmount().divide(new BigDecimal(360), 8, BigDecimal.ROUND_HALF_UP));
        lockOrder.setStatus(1); // 锁仓中
        lockOrder.setCreateTime(new Date());
        lockOrderMapper.insert(lockOrder);
        return lockOrder;
    }

    @Test
    @DisplayName("测试T+1结算：待确认质押转为已确认质押")
    void testSettlePendingStake() {
        // 创建测试用户
        User testUser = createTestUser(1001L, 0, 0);
        
        // 创建测试质押用户，有待确认质押
        StakeUser testStakeUser = createTestStakeUser(1001L, 
            new BigDecimal("1000"), // 已确认质押
            new BigDecimal("500"),  // 待确认质押
            new BigDecimal("100"),  // 静态池
            new BigDecimal("50")    // 动态池
        );

        // 记录初始状态
        BigDecimal initialCurrentAmount = testStakeUser.getCurrentAmount();
        BigDecimal initialPendingAmount = testStakeUser.getPendingAmount();

        System.out.println("=== T+1结算测试 ===");
        System.out.println("初始已确认质押: " + initialCurrentAmount);
        System.out.println("初始待确认质押: " + initialPendingAmount);

        // 执行T+1结算
        job30.executeSettlementTransaction();

        // 查询结算后的数据
        StakeUser afterSettlement = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 1001L)
        );

        // 验证结果
        assertNotNull(afterSettlement);
        BigDecimal expectedCurrentAmount = initialCurrentAmount.add(initialPendingAmount);
        assertEquals(0, expectedCurrentAmount.compareTo(afterSettlement.getCurrentAmount()));
        assertEquals(0, BigDecimal.ZERO.compareTo(afterSettlement.getPendingAmount()));

        System.out.println("结算后已确认质押: " + afterSettlement.getCurrentAmount());
        System.out.println("结算后待确认质押: " + afterSettlement.getPendingAmount());
        System.out.println("✅ T+1结算测试通过");
    }

    @Test
    @DisplayName("测试静态收益计算：基础收益计算")
    void testCalculateStakeStaticRewards_BasicReward() {
        // 创建测试用户（无城主加成）
        User testUser = createTestUser(1002L, 0, 0);
        
        // 创建测试质押用户
        StakeUser testStakeUser = createTestStakeUser(1002L, 
            new BigDecimal("1000"), // 已确认质押
            BigDecimal.ZERO,        // 无待确认质押
            new BigDecimal("100"),  // 静态池
            BigDecimal.ZERO         // 动态池
        );

        // 获取系统收益率
        SysConfig sysConfig = sysConfigService.getSysConfig();
        BigDecimal rewardRate = sysConfig.getRewardRate();
        
        // 计算预期收益
        BigDecimal rewardBase = testStakeUser.getCurrentAmount().add(testStakeUser.getStaticPool());
        BigDecimal expectedStaticReward = rewardBase.multiply(rewardRate);

        System.out.println("=== 基础静态收益测试 ===");
        System.out.println("收益率: " + rewardRate);
        System.out.println("收益基数: " + rewardBase + " (已确认质押: " + testStakeUser.getCurrentAmount() + " + 静态池: " + testStakeUser.getStaticPool() + ")");
        System.out.println("预期基础收益: " + expectedStaticReward);

        // 执行结算
        job30.executeSettlementTransaction();

        // 查询结算后的数据
        StakeUser afterSettlement = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 1002L)
        );

        // 验证结果
        assertNotNull(afterSettlement);
        assertTrue(afterSettlement.getTodayStatic().compareTo(BigDecimal.ZERO) > 0);
        assertEquals(expectedStaticReward, afterSettlement.getTodayStatic());
        
        // 验证静态池增加
        BigDecimal expectedNewStaticPool = testStakeUser.getStaticPool().add(expectedStaticReward);
        assertEquals(expectedNewStaticPool, afterSettlement.getStaticPool());

        System.out.println("实际今日静态收益: " + afterSettlement.getTodayStatic());
        System.out.println("实际静态池: " + afterSettlement.getStaticPool());
        System.out.println("✅ 基础静态收益测试通过");
    }

    @Test
    @DisplayName("测试静态收益计算：城主加成4倍收益")
    void testCalculateStakeStaticRewards_CityLordBonus() {
        // 创建测试用户（有城主加成次数）
        User testUser = createTestUser(1003L, 0, 2);
        
        // 创建测试质押用户
        StakeUser testStakeUser = createTestStakeUser(1003L, 
            new BigDecimal("1000"), // 已确认质押
            BigDecimal.ZERO,        // 无待确认质押
            new BigDecimal("100"),  // 静态池
            BigDecimal.ZERO         // 动态池
        );

        // 获取系统收益率
        SysConfig sysConfig = sysConfigService.getSysConfig();
        BigDecimal rewardRate = sysConfig.getRewardRate();
        
        // 计算预期收益
        BigDecimal rewardBase = testStakeUser.getCurrentAmount().add(testStakeUser.getStaticPool());
        BigDecimal baseReward = rewardBase.multiply(rewardRate);
        BigDecimal expectedBonusReward = baseReward.multiply(new BigDecimal("4")); // 城主加成4倍

        System.out.println("=== 城主加成静态收益测试 ===");
        System.out.println("初始城主加成次数: " + testUser.getQuadrupleRewardTimes());
        System.out.println("收益基数: " + rewardBase);
        System.out.println("基础收益: " + baseReward);
        System.out.println("预期城主加成收益(4倍): " + expectedBonusReward);

        // 执行结算
        job30.executeSettlementTransaction();

        // 查询结算后的数据
        StakeUser afterSettlement = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 1003L)
        );
        User afterUser = userMapper.selectById(1003L);

        // 验证结果
        assertNotNull(afterSettlement);
        assertNotNull(afterUser);
        
        // 验证收益是4倍
        assertEquals(expectedBonusReward, afterSettlement.getTodayStatic());
        
        // 验证城主加成次数被消耗
        assertEquals(1, afterUser.getQuadrupleRewardTimes()); // 从2次减少到1次
        
        // 验证静态池增加
        BigDecimal expectedNewStaticPool = testStakeUser.getStaticPool().add(expectedBonusReward);
        assertEquals(expectedNewStaticPool, afterSettlement.getStaticPool());

        // 验证日志记录
        List<UserLog> logs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, 1003L)
                .like(UserLog::getRemark, "城主加成")
        );
        assertTrue(logs.size() > 0);

        System.out.println("实际今日静态收益: " + afterSettlement.getTodayStatic());
        System.out.println("剩余城主加成次数: " + afterUser.getQuadrupleRewardTimes());
        System.out.println("城主加成日志数量: " + logs.size());
        System.out.println("✅ 城主加成静态收益测试通过");
    }

    @Test
    @DisplayName("测试锁仓订单线性释放")
    void testLockOrderLinearRelease() {
        // 创建测试用户
        User testUser = createTestUser(1005L, 0, 0);
        
        // 创建锁仓订单
        LockOrder testLockOrder = createTestLockOrder(1005L, 
            new BigDecimal("3600"), // 锁仓总额3600U
            new BigDecimal("100"),  // 已释放100U
            new BigDecimal("50"),   // 可提取50U
            360, 10                 // 总360天，已释放10天
        );

        System.out.println("=== 锁仓订单线性释放测试 ===");
        System.out.println("初始状态:");
        System.out.println("  锁仓总额: " + testLockOrder.getLockAmount());
        System.out.println("  已释放金额: " + testLockOrder.getReleasedAmount());
        System.out.println("  可提取金额: " + testLockOrder.getAvailableAmount());
        System.out.println("  已释放天数: " + testLockOrder.getReleasedDays());

        // 执行结算（包含线性释放）
        job30.executeSettlementTransaction();

        // 查询结算后的锁仓订单
        LockOrder afterRelease = lockOrderMapper.selectOne(
            new LambdaQueryWrapper<LockOrder>().eq(LockOrder::getUserId, 1005L)
        );

        // 验证释放结果
        assertNotNull(afterRelease);
        assertTrue(afterRelease.getReleasedAmount().compareTo(testLockOrder.getReleasedAmount()) >= 0);
        assertTrue(afterRelease.getAvailableAmount().compareTo(testLockOrder.getAvailableAmount()) >= 0);

        System.out.println("释放后状态:");
        System.out.println("  已释放金额: " + afterRelease.getReleasedAmount());
        System.out.println("  可提取金额: " + afterRelease.getAvailableAmount());
        System.out.println("  已释放天数: " + afterRelease.getReleasedDays());
        System.out.println("✅ 锁仓订单线性释放测试通过");
    }

    @Test
    @DisplayName("综合测试：完整的质押收益结算流程")
    void testCompleteStakeSettlement() {
        // 创建测试用户（有城主加成）
        User testUser = createTestUser(1010L, 1, 1);
        
        // 创建测试质押用户（有待确认质押）
        StakeUser testStakeUser = createTestStakeUser(1010L, 
            new BigDecimal("1000"), // 已确认质押
            new BigDecimal("500"),  // 待确认质押
            new BigDecimal("100"),  // 静态池
            new BigDecimal("50")    // 动态池
        );

        // 创建用户关系
        createUserRelation(1010L, 0L, "/0");

        // 创建直推用户（用于动态收益计算）
        User directUser1 = createTestUser(1011L, 0, 0);
        StakeUser directStakeUser1 = createTestStakeUser(1011L, 
            new BigDecimal("200"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(1011L, 1010L, "/0/1010");

        User directUser2 = createTestUser(1012L, 0, 0);
        StakeUser directStakeUser2 = createTestStakeUser(1012L, 
            new BigDecimal("300"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(1012L, 1010L, "/0/1010");

        // 创建锁仓订单
        LockOrder testLockOrder = createTestLockOrder(1010L, 
            new BigDecimal("1000"), // 锁仓总额
            new BigDecimal("100"),  // 已释放
            new BigDecimal("50"),   // 可提取
            360, 30                 // 总天数，已释放天数
        );

        // 记录初始状态
        System.out.println("=== 综合结算测试 ===");
        System.out.println("初始状态:");
        System.out.println("  用户ID: " + testUser.getId());
        System.out.println("  城主加成次数: " + testUser.getQuadrupleRewardTimes());
        System.out.println("  已确认质押: " + testStakeUser.getCurrentAmount());
        System.out.println("  待确认质押: " + testStakeUser.getPendingAmount());
        System.out.println("  静态池: " + testStakeUser.getStaticPool());
        System.out.println("  动态池: " + testStakeUser.getDynamicPool());
        System.out.println("  锁仓可提取: " + testLockOrder.getAvailableAmount());

        // 执行完整结算
        job30.executeSettlementTransaction();

        // 查询结算后的数据
        StakeUser afterStakeUser = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 1010L)
        );
        User afterUser = userMapper.selectById(1010L);
        LockOrder afterLockOrder = lockOrderMapper.selectOne(
            new LambdaQueryWrapper<LockOrder>().eq(LockOrder::getUserId, 1010L)
        );

        // 验证结果
        assertNotNull(afterStakeUser);
        assertNotNull(afterUser);
        assertNotNull(afterLockOrder);

        System.out.println("结算后状态:");
        System.out.println("  剩余城主加成次数: " + afterUser.getQuadrupleRewardTimes());
        System.out.println("  已确认质押: " + afterStakeUser.getCurrentAmount());
        System.out.println("  待确认质押: " + afterStakeUser.getPendingAmount());
        System.out.println("  静态池: " + afterStakeUser.getStaticPool());
        System.out.println("  今日静态收益: " + afterStakeUser.getTodayStatic());
        System.out.println("  锁仓静态池: " + afterStakeUser.getLockStaticPool());
        System.out.println("  锁仓可提取: " + afterLockOrder.getAvailableAmount());

        // 验证T+1结算
        BigDecimal expectedCurrentAmount = new BigDecimal("1500"); // 1000 + 500
        assertEquals(0, expectedCurrentAmount.compareTo(afterStakeUser.getCurrentAmount()));
        assertEquals(0, BigDecimal.ZERO.compareTo(afterStakeUser.getPendingAmount()));

        // 验证静态收益计算
        assertTrue(afterStakeUser.getTodayStatic().compareTo(BigDecimal.ZERO) > 0);
        
        // 验证城主加成被消耗
        assertEquals(0, afterUser.getQuadrupleRewardTimes());

        // 验证锁仓订单释放
        assertTrue(afterLockOrder.getAvailableAmount().compareTo(testLockOrder.getAvailableAmount()) >= 0);

        // 验证日志记录
        List<UserLog> logs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>().eq(UserLog::getUserId, 1010L)
        );
        assertTrue(logs.size() > 0);
        System.out.println("  生成日志数量: " + logs.size());

        System.out.println("✅ 综合结算测试通过");
    }

    @Test
    @DisplayName("测试锁仓订单静态收益计算")
    void testLockOrderStaticRewards() {
        // 创建测试用户（无城主加成，便于计算验证）
        User testUser = createTestUser(1006L, 0, 0);
        
        // 创建锁仓订单（有可提取金额）
        LockOrder testLockOrder = createTestLockOrder(1006L, 
            new BigDecimal("1000"), // 锁仓总额
            new BigDecimal("200"),  // 已释放200U
            new BigDecimal("100"),  // 可提取100U（这是收益计算基数）
            360, 72                 // 总360天，已释放72天
        );

        // 获取系统收益率
        SysConfig sysConfig = sysConfigService.getSysConfig();
        BigDecimal rewardRate = sysConfig.getRewardRate();
        
        // 计算预期锁仓静态收益
        // 锁仓订单静态收益 = 可提取金额 * 收益率 * 2（锁仓订单是2倍收益）
        BigDecimal availableAmount = testLockOrder.getAvailableAmount();
        BigDecimal expectedLockStaticReward = availableAmount.multiply(rewardRate).multiply(new BigDecimal("2"));

        System.out.println("=== 锁仓订单静态收益测试 ===");
        System.out.println("锁仓订单信息:");
        System.out.println("  锁仓总额: " + testLockOrder.getLockAmount());
        System.out.println("  已释放金额: " + testLockOrder.getReleasedAmount());
        System.out.println("  可提取金额: " + availableAmount + " (收益计算基数)");
        System.out.println("  系统收益率: " + rewardRate);
        System.out.println("  锁仓收益倍数: 2倍");
        System.out.println("  预期锁仓静态收益: " + availableAmount + " × " + rewardRate + " × 2 = " + expectedLockStaticReward);

        // 查询结算前的StakeUser状态（可能不存在）
        StakeUser beforeSettlement = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 1006L)
        );
        
        BigDecimal beforeLockStaticPool = BigDecimal.ZERO;
        BigDecimal beforeTotalStatic = BigDecimal.ZERO;
        if (beforeSettlement != null) {
            beforeLockStaticPool = beforeSettlement.getLockStaticPool() != null ? beforeSettlement.getLockStaticPool() : BigDecimal.ZERO;
            beforeTotalStatic = beforeSettlement.getTotalStatic() != null ? beforeSettlement.getTotalStatic() : BigDecimal.ZERO;
        }

        System.out.println("结算前状态:");
        System.out.println("  锁仓静态池: " + beforeLockStaticPool);
        System.out.println("  累计静态收益: " + beforeTotalStatic);

        // 执行结算
        job30.executeSettlementTransaction();

        // 查询结算后的质押用户数据
        StakeUser afterSettlement = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 1006L)
        );

        // 验证锁仓静态收益
        assertNotNull(afterSettlement, "结算后应该创建StakeUser记录");
        
        BigDecimal afterLockStaticPool = afterSettlement.getLockStaticPool() != null ? afterSettlement.getLockStaticPool() : BigDecimal.ZERO;
        BigDecimal afterTotalStatic = afterSettlement.getTotalStatic() != null ? afterSettlement.getTotalStatic() : BigDecimal.ZERO;
        
        // 计算实际收益增量
        BigDecimal actualLockStaticIncrease = afterLockStaticPool.subtract(beforeLockStaticPool);
        BigDecimal actualTotalStaticIncrease = afterTotalStatic.subtract(beforeTotalStatic);

        System.out.println("结算后状态:");
        System.out.println("  锁仓静态池: " + afterLockStaticPool);
        System.out.println("  累计静态收益: " + afterTotalStatic);
        
        System.out.println("收益变化:");
        System.out.println("  锁仓静态池增加: " + beforeLockStaticPool + " → " + afterLockStaticPool + " (+" + actualLockStaticIncrease + ")");
        System.out.println("  累计静态收益增加: " + beforeTotalStatic + " → " + afterTotalStatic + " (+" + actualTotalStaticIncrease + ")");
        
        // 验证收益计算是否正确
        // 注意：由于线性释放会增加可提取金额，实际收益可能大于预期收益
        assertTrue(actualLockStaticIncrease.compareTo(BigDecimal.ZERO) > 0, 
            "锁仓静态收益应该大于0");
        assertEquals(0, actualLockStaticIncrease.compareTo(actualTotalStaticIncrease), 
            "累计静态收益增加应该等于锁仓静态收益增加");
        
        // 计算实际收益率验证
        LockOrder afterLockOrder = lockOrderMapper.selectOne(
            new LambdaQueryWrapper<LockOrder>().eq(LockOrder::getUserId, 1006L)
        );
        if (afterLockOrder != null) {
            BigDecimal actualAvailableAmount = afterLockOrder.getAvailableAmount();
            BigDecimal calculatedReward = actualAvailableAmount.multiply(rewardRate).multiply(new BigDecimal("2"));
            System.out.println("  实际可提取金额: " + actualAvailableAmount);
            System.out.println("  基于实际可提取金额计算的收益: " + calculatedReward);
            
            // 验证收益计算逻辑正确（考虑精度问题）
            BigDecimal calculatedRewardRounded = calculatedReward.setScale(8, BigDecimal.ROUND_HALF_UP);
            assertEquals(0, calculatedRewardRounded.compareTo(actualLockStaticIncrease), 
                "基于实际可提取金额的收益计算应该正确");
        }

        // 验证日志记录
        List<UserLog> logs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, 1006L)
                .like(UserLog::getRemark, "锁仓订单静态收益")
        );
        
        System.out.println("  生成日志数量: " + logs.size());
        if (logs.size() > 0) {
            System.out.println("  日志内容: " + logs.get(0).getRemark());
            System.out.println("  日志金额: " + logs.get(0).getAmount());
        }

        System.out.println("✅ 锁仓订单静态收益测试通过");
    }

    @Test
    @DisplayName("测试动态收益：社区奖励、分享奖励、平级奖励流水验证")
    void testDynamicRewardsWithUserLogs() {
        System.out.println("=== 动态收益流水验证测试 ===");
        
        // 创建一个V4级别的主用户（可以获得平级奖励）
        User mainUser = createTestUser(5001L, 4, 0);
        StakeUser mainStakeUser = createTestStakeUser(5001L, 
            new BigDecimal("20000"), // V4个人质押要求
            BigDecimal.ZERO, 
            new BigDecimal("100"),   // 静态池，用于产生静态收益
            BigDecimal.ZERO
        );
        createUserRelation(5001L, 0L, "/0/5001");

        // 创建6个有效直推用户（质押>=100U），满足分享奖励条件
        for (int i = 1; i <= 6; i++) {
            User directUser = createTestUser(5001L + i, 1, 0); // V1级别
            StakeUser directStakeUser = createTestStakeUser(5001L + i, 
                new BigDecimal("200"), // 有效用户
                BigDecimal.ZERO, 
                new BigDecimal("10"),  // 静态池，产生静态收益
                BigDecimal.ZERO
            );
            createUserRelation(5001L + i, 5001L, "/0/5001/" + (5001L + i));
            
            // 为每个直推用户创建团队，满足V1社区业绩要求
            for (int j = 1; j <= 10; j++) {
                User teamUser = createTestUser(5001L + i * 100 + j, 0, 0);
                StakeUser teamStakeUser = createTestStakeUser(5001L + i * 100 + j, 
                    new BigDecimal("1000"), 
                    BigDecimal.ZERO, 
                    new BigDecimal("5"), // 产生静态收益
                    BigDecimal.ZERO
                );
                createUserRelation(5001L + i * 100 + j, 5001L + i, "/0/5001/" + (5001L + i) + "/" + (5001L + i * 100 + j));
            }
        }

        // 创建同级别用户（V4）用于平级奖励
        for (int i = 1; i <= 3; i++) {
            User peerUser = createTestUser(6000L + i, 4, 0); // V4级别
            StakeUser peerStakeUser = createTestStakeUser(6000L + i, 
                new BigDecimal("20000"), 
                BigDecimal.ZERO, 
                new BigDecimal("200"), // 产生静态收益，用于平级奖励计算
                BigDecimal.ZERO
            );
            createUserRelation(6000L + i, 5001L, "/0/5001/" + (6000L + i));
            
            // 为同级别用户创建团队，满足V4社区业绩要求
            for (int j = 1; j <= 50; j++) {
                User teamUser = createTestUser(6000L + i * 1000 + j, 0, 0);
                StakeUser teamStakeUser = createTestStakeUser(6000L + i * 1000 + j, 
                    new BigDecimal("6000"), 
                    BigDecimal.ZERO, 
                    BigDecimal.ZERO, 
                    BigDecimal.ZERO
                );
                createUserRelation(6000L + i * 1000 + j, 6000L + i, "/0/5001/" + (6000L + i) + "/" + (6000L + i * 1000 + j));
            }
        }

        // 额外创建团队用户以满足主用户的V4社区业绩要求（30W）
        for (int i = 1; i <= 100; i++) {
            User teamUser = createTestUser(70000L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(70000L + i,
                new BigDecimal("3000"), 
                BigDecimal.ZERO, 
                new BigDecimal("2"), // 产生静态收益
                BigDecimal.ZERO
            );
            createUserRelation(70000L + i, 5001L, "/0/5001/" + (70000L + i));
        }

        System.out.println("测试数据准备完成:");
        System.out.println("  主用户: " + mainUser.getId() + " (V4级别)");
        System.out.println("  个人质押: " + mainStakeUser.getCurrentAmount());
        System.out.println("  静态池: " + mainStakeUser.getStaticPool());

        // 计算预期条件
        BigDecimal communityPerf = job30.getCommunityPerformance(5001L);
        List<Long> directUserIds = job30.getDirectUserIds(5001L);
        int validDirectUsers = stakeUserMapper.selectCount(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, directUserIds)
                .ge(StakeUser::getCurrentAmount, new BigDecimal("100"))
        ).intValue();

        System.out.println("  社区业绩: " + communityPerf);
        System.out.println("  直推用户数量: " + directUserIds.size());
        System.out.println("  有效直推用户: " + validDirectUsers + " (质押>=100U)");

        // 检查分享奖励条件
        BigDecimal shareRewardRate = BigDecimal.ZERO;
        if (mainStakeUser.getCurrentAmount().compareTo(new BigDecimal("1000")) >= 0 && validDirectUsers >= 6) {
            shareRewardRate = new BigDecimal("0.05"); // 5%
        }
        System.out.println("  分享奖励比例: " + shareRewardRate.multiply(new BigDecimal("100")) + "%");

        // 检查社区奖励条件（V4 = 50%）
        BigDecimal communityRewardRate = new BigDecimal("0.50"); // V4级别50%
        System.out.println("  社区奖励比例: " + communityRewardRate.multiply(new BigDecimal("100")) + "%");

        // 检查平级奖励条件（V4-V6有5%平级奖励）
        System.out.println("  平级奖励比例: 5% (V4级别)");

        // 清除之前的日志记录
        userLogMapper.delete(new LambdaQueryWrapper<UserLog>().eq(UserLog::getUserId, 5001L));

        // 执行结算
        System.out.println("\n开始执行结算...");
        job30.executeSettlementTransaction();

        // 查询结算后的日志记录
        List<UserLog> allLogs = userLogMapper.selectList(
            new LambdaQueryWrapper<UserLog>()
                .eq(UserLog::getUserId, 5001L)
                .orderByAsc(UserLog::getCreateTime)
        );

        System.out.println("\n=== 结算后日志分析 ===");
        System.out.println("总日志数量: " + allLogs.size());

        // 分类统计日志
        List<UserLog> staticLogs = allLogs.stream()
            .filter(log -> log.getRemark().contains("静态收益"))
            .collect(Collectors.toList());

        List<UserLog> shareLogs = allLogs.stream()
            .filter(log -> log.getRemark().contains("分享奖励"))
            .collect(Collectors.toList());

        List<UserLog> communityLogs = allLogs.stream()
            .filter(log -> log.getRemark().contains("社区奖励"))
            .collect(Collectors.toList());

        List<UserLog> peerLogs = allLogs.stream()
            .filter(log -> log.getRemark().contains("平级奖励"))
            .collect(Collectors.toList());

        List<UserLog> cityLordLogs = allLogs.stream()
            .filter(log -> log.getRemark().contains("城主加成"))
            .collect(Collectors.toList());

        System.out.println("\n日志分类统计:");
        System.out.println("  静态收益日志: " + staticLogs.size() + "条");
        System.out.println("  分享奖励日志: " + shareLogs.size() + "条");
        System.out.println("  社区奖励日志: " + communityLogs.size() + "条");
        System.out.println("  平级奖励日志: " + peerLogs.size() + "条");
        System.out.println("  城主加成日志: " + cityLordLogs.size() + "条");

        // 详细显示每种奖励的金额
        System.out.println("\n详细奖励金额:");
        
        if (!staticLogs.isEmpty()) {
            BigDecimal totalStatic = staticLogs.stream()
                .map(UserLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            System.out.println("  静态收益总额: " + totalStatic);
            staticLogs.forEach(log -> System.out.println("    - " + log.getRemark() + ": " + log.getAmount()));
        }

        if (!shareLogs.isEmpty()) {
            BigDecimal totalShare = shareLogs.stream()
                .map(UserLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            System.out.println("  分享奖励总额: " + totalShare);
            shareLogs.forEach(log -> System.out.println("    - " + log.getRemark() + ": " + log.getAmount()));
        }

        if (!communityLogs.isEmpty()) {
            BigDecimal totalCommunity = communityLogs.stream()
                .map(UserLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            System.out.println("  社区奖励总额: " + totalCommunity);
            communityLogs.forEach(log -> System.out.println("    - " + log.getRemark() + ": " + log.getAmount()));
        }

        if (!peerLogs.isEmpty()) {
            BigDecimal totalPeer = peerLogs.stream()
                .map(UserLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            System.out.println("  平级奖励总额: " + totalPeer);
            peerLogs.forEach(log -> System.out.println("    - " + log.getRemark() + ": " + log.getAmount()));
        }

        if (!cityLordLogs.isEmpty()) {
            BigDecimal totalCityLord = cityLordLogs.stream()
                .map(UserLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            System.out.println("  城主加成总额: " + totalCityLord);
            cityLordLogs.forEach(log -> System.out.println("    - " + log.getRemark() + ": " + log.getAmount()));
        }

        // 验证期望的日志数量
        System.out.println("\n=== 验证结果 ===");

        // 验证静态收益日志（至少1条）
        assertTrue(staticLogs.size() >= 1, "应该有静态收益日志");
        System.out.println("✅ 静态收益日志验证通过");

        // 验证分享奖励日志（如果满足条件应该有1条）
        if (shareRewardRate.compareTo(BigDecimal.ZERO) > 0) {
            assertTrue(shareLogs.size() >= 1, "满足条件时应该有分享奖励日志");
            System.out.println("✅ 分享奖励日志验证通过");
        } else {
            System.out.println("⚠️ 不满足分享奖励条件，无分享奖励日志");
        }

        // 验证社区奖励日志（V4级别应该有1条）
        assertTrue(communityLogs.size() >= 1, "V4级别应该有社区奖励日志");
        System.out.println("✅ 社区奖励日志验证通过");

        // 验证平级奖励日志（V4级别应该有1条）
        assertTrue(peerLogs.size() >= 1, "V4级别应该有平级奖励日志");
        System.out.println("✅ 平级奖励日志验证通过");

        // 查询结算后的StakeUser状态
        StakeUser afterStakeUser = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, 5001L)
        );

        System.out.println("\n结算后StakeUser状态:");
        System.out.println("  静态池: " + afterStakeUser.getStaticPool());
        System.out.println("  动态池: " + afterStakeUser.getDynamicPool());
        System.out.println("  今日静态: " + afterStakeUser.getTodayStatic());
        System.out.println("  今日动态: " + afterStakeUser.getTodayDynamic());
        System.out.println("  累计静态: " + afterStakeUser.getTotalStatic());
        System.out.println("  累计动态: " + afterStakeUser.getTotalDynamic());

        System.out.println("✅ 动态收益流水验证测试通过");
    }

    @Test
    @DisplayName("测试用户等级升级：V0到V6完整升级流程")
    void testUserLevelUpgrade() {
        System.out.println("=== 用户等级升级测试 ===");
        
        // 测试V1升级：个人100U + 社区1W
//        testLevelUpgrade_V1();
        
        // 测试V2升级：个人1000U + 社区3W + 2个V1
//        testLevelUpgrade_V2();
        
        // 测试V3升级：个人5000U + 社区10W + 2个V2
        testLevelUpgrade_V3();
        
        // 测试V4升级：个人20000U + 社区30W + 3个V3
        testLevelUpgrade_V4();
        
        // 测试V5升级：个人50000U + 社区100W + 3个V4
        testLevelUpgrade_V5();
        
        // 测试V6升级：个人100000U + 社区300W + 3个V5
        testLevelUpgrade_V6();
        
        System.out.println("✅ 用户等级升级测试全部通过");
    }

    private void testLevelUpgrade_V1() {
        System.out.println("\n--- V1等级升级测试 ---");
        System.out.println("升级条件：个人锁仓USD 100U + 社区锁仓USD业绩1W");
        
        // 创建主用户
        User mainUser = createTestUser(2001L, 0, 0);
        // 创建质押用户记录（用于升级计算）
        StakeUser mainStakeUser = createTestStakeUser(2001L, 
            BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        // 创建锁仓订单（个人锁仓USD 100U）
        LockOrder mainLockOrder = createTestLockOrderForUpgrade(2001L, new BigDecimal("100"));
        createUserRelation(2001L, 0L, "/0");

        // 创建团队用户以达到社区锁仓USD业绩要求（1W）
        for (int i = 1; i <= 5; i++) {
            User teamUser = createTestUser(2001L + i, 0, 0);
            // 创建质押用户记录
            StakeUser teamStakeUser = createTestStakeUser(2001L + i, 
                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            // 创建锁仓订单（每人2000U）
            createTestLockOrderForUpgrade(2001L + i, new BigDecimal("2000"));
            createUserRelation(2001L + i, 2001L, "/0/2001/" + (2001L + i));
        }

        System.out.println("升级前等级: V" + mainUser.getLevel());
        System.out.println("个人锁仓USD: " + mainLockOrder.getUsdAmount());
        
        // 执行结算（包含等级更新）
        job30.executeSettlementTransaction();

        // 验证升级结果
        User afterUser = userMapper.selectById(2001L);
        System.out.println("升级后等级: V" + afterUser.getLevel());
        assertEquals(1, afterUser.getLevel(), "应该升级到V1");
        System.out.println("✅ V1升级成功");
    }

    private void testLevelUpgrade_V2() {
        System.out.println("\n--- V2等级升级测试 ---");
        System.out.println("升级条件：个人锁仓USD 1000U + 社区锁仓USD业绩3W + 2个V1直推");
        
        // 创建主用户
        User mainUser = createTestUser(2010L, 0, 0);
        StakeUser mainStakeUser = createTestStakeUser(2010L, 
            BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        // 创建锁仓订单（个人锁仓USD 1000U）
        LockOrder mainLockOrder = createTestLockOrderForUpgrade(2010L, new BigDecimal("1000"));
        createUserRelation(2010L, 0L, "/0");

        // 创建2个V1直推用户（确保他们满足V1条件）
        for (int i = 1; i <= 2; i++) {
            User directUser = createTestUser(2010L + i, 1, 0); // 设置为V1级别
            StakeUser directStakeUser = createTestStakeUser(2010L + i, 
                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            // 创建锁仓订单（个人锁仓USD 100U，满足V1条件）
            createTestLockOrderForUpgrade(2010L + i, new BigDecimal("100"));
            createUserRelation(2010L + i, 2010L, "/0/2010/" + (2010L + i));
            
            // 为每个V1用户创建团队以满足V1的社区锁仓USD业绩要求（1W）
            for (int j = 1; j <= 5; j++) {
                User teamUser = createTestUser(2010L + i * 10 + j, 0, 0);
                StakeUser teamStakeUser = createTestStakeUser(2010L + i * 10 + j, 
                    BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                // 创建锁仓订单（每人2000U）
                createTestLockOrderForUpgrade(2010L + i * 10 + j, new BigDecimal("2000"));
                createUserRelation(2010L + i * 10 + j, 2010L + i, "/0/2010/" + (2010L + i) + "/" + (2010L + i * 10 + j));
            }
        }

        // 额外创建团队用户以达到3W社区锁仓USD业绩（需要额外10000U）
        for (int i = 1; i <= 5; i++) {
            User teamUser = createTestUser(2050L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(2050L + i, 
                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            // 创建锁仓订单（每人2000U）
            createTestLockOrderForUpgrade(2050L + i, new BigDecimal("2000"));
            createUserRelation(2050L + i, 2010L, "/0/2010/" + (2050L + i));
        }

        System.out.println("升级前等级: V" + mainUser.getLevel());
        System.out.println("个人锁仓USD: " + mainLockOrder.getUsdAmount());
        
        // 手动检查升级条件
        BigDecimal communityPerf = job30.getCommunityLockUsdPerformance(2010L);
        System.out.println("社区锁仓USD业绩: " + communityPerf);
        
        // 检查直推用户
        List<Long> directUserIds = job30.getDirectUserIds(2010L);
        System.out.println("直推用户数量: " + directUserIds.size());
        System.out.println("直推用户ID: " + directUserIds);
        
        for (Long directUserId : directUserIds) {
            User directUser = userMapper.selectById(directUserId);
            BigDecimal directPersonalLockUsd = lockOrderMapper.getUserTotalUsdAmount(directUserId);
            BigDecimal directCommunityPerf = job30.getCommunityLockUsdPerformance(directUserId);
            int directLevel = job30.getUserLevel(
                directPersonalLockUsd,
                directCommunityPerf,
                directUserId
            );
            System.out.println("直推用户" + directUserId + ": 当前等级V" + directUser.getLevel() + 
                ", 个人锁仓USD=" + directPersonalLockUsd +
                ", 社区锁仓USD业绩=" + directCommunityPerf + ", 计算等级V" + directLevel);
        }
        
        // 执行结算
        job30.executeSettlementTransaction();

        // 验证升级结果
        User afterUser = userMapper.selectById(2010L);
        System.out.println("升级后等级: V" + afterUser.getLevel());
        
        // 如果升级失败，再次检查条件
        if (afterUser.getLevel() != 2) {
            System.out.println("V2升级失败，重新检查条件:");
            System.out.println("  个人质押1000U: " + mainStakeUser.getCurrentAmount() + " >= 1000? " + 
                (mainStakeUser.getCurrentAmount().compareTo(new BigDecimal("1000")) >= 0));
            System.out.println("  社区业绩3W: " + communityPerf + " >= 30000? " + 
                (communityPerf.compareTo(new BigDecimal("30000")) >= 0));
            
            int v1Count = 0;
            for (Long directUserId : directUserIds) {
                User directUser = userMapper.selectById(directUserId);
                if (directUser.getLevel() >= 1) {
                    v1Count++;
                }
            }
            System.out.println("  V1直推用户: " + v1Count + " >= 2? " + (v1Count >= 2));
        }
        
        assertEquals(2, afterUser.getLevel(), "应该升级到V2");
        System.out.println("✅ V2升级成功");
    }

    private void testLevelUpgrade_V3() {
        System.out.println("\n--- V3等级升级测试 ---");
        System.out.println("升级条件：个人质押5000U + 社区业绩10W + 2个V2直推");
        
        // 创建主用户
        User mainUser = createTestUser(2100L, 2, 0);
        StakeUser mainStakeUser = createTestStakeUser(2100L, 
            new BigDecimal("5000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(2100L, 0L, "/0");

        // 创建2个V2直推用户（通过实际满足V2条件）
        for (int i = 1; i <= 2; i++) {
            User directUser = createTestUser(2100L + i, 2, 0); // 初始为V0
            StakeUser directStakeUser = createTestStakeUser(2100L + i, 
                new BigDecimal("1000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(2100L + i, 2100L, "/0/2100/" + (2100L + i));
            
            // 为每个V2用户创建个人锁仓（1000U）和团队以满足V2条件
            createTestLockOrderForUpgrade(2100L + i, new BigDecimal("1000"));
            
            // 为每个V2用户创建团队以满足V2的社区业绩要求（3W）
            for (int j = 1; j <= 15; j++) {
                User teamUser = createTestUser(2100L + i * 1000 + j, 0, 0);
                StakeUser teamStakeUser = createTestStakeUser(2100L + i * 1000 + j, 
                    BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                createTestLockOrderForUpgrade(2100L + i * 1000 + j, new BigDecimal("2000"));
                createUserRelation(2100L + i * 1000 + j, 2100L + i, "/0/2100/" + (2100L + i) + "/" + (2100L + i * 1000 + j));
            }
        }

        // 创建大量团队用户以达到10W社区业绩
        for (int i = 1; i <= 25; i++) {
            User teamUser = createTestUser(2200L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(2200L + i, 
                new BigDecimal("4000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(2200L + i, 2100L, "/0/2100/" + (2200L + i));
        }

        System.out.println("升级前等级: V" + mainUser.getLevel());
        System.out.println("个人质押: " + mainStakeUser.getCurrentAmount());
        
        // 手动检查升级条件
        BigDecimal communityPerf = job30.getCommunityPerformance(2100L);
        System.out.println("社区业绩: " + communityPerf);
        
        // 检查直推用户
        List<Long> directUserIds = job30.getDirectUserIds(2100L);
        System.out.println("直推用户数量: " + directUserIds.size());
        System.out.println("直推用户ID: " + directUserIds);
        
        for (Long directUserId : directUserIds) {
            User directUser = userMapper.selectById(directUserId);
            StakeUser directStakeUser = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, directUserId)
            );
            BigDecimal directCommunityPerf = job30.getCommunityPerformance(directUserId);
            int directLevel = job30.getUserLevel(
                directStakeUser != null ? directStakeUser.getCurrentAmount() : BigDecimal.ZERO,
                directCommunityPerf,
                directUserId
            );
            System.out.println("直推用户" + directUserId + ": 当前等级V" + directUser.getLevel() + 
                ", 个人质押=" + (directStakeUser != null ? directStakeUser.getCurrentAmount() : "0") +
                ", 社区业绩=" + directCommunityPerf + ", 计算等级V" + directLevel);
        }
        
        // 执行结算
        job30.executeSettlementTransaction();

        // 验证升级结果
        User afterUser = userMapper.selectById(2100L);
        System.out.println("升级后等级: V" + afterUser.getLevel());
        
        // 如果升级失败，再次检查条件
        if (afterUser.getLevel() != 3) {
            System.out.println("V3升级失败，重新检查条件:");
            System.out.println("  个人质押5000U: " + mainStakeUser.getCurrentAmount() + " >= 5000? " + 
                (mainStakeUser.getCurrentAmount().compareTo(new BigDecimal("5000")) >= 0));
            System.out.println("  社区业绩10W: " + communityPerf + " >= 100000? " + 
                (communityPerf.compareTo(new BigDecimal("100000")) >= 0));
            
            int v2Count = 0;
            for (Long directUserId : directUserIds) {
                User directUser = userMapper.selectById(directUserId);
                if (directUser.getLevel() >= 2) {
                    v2Count++;
                }
            }
            System.out.println("  V2直推用户: " + v2Count + " >= 2? " + (v2Count >= 2));
        }
        
        assertEquals(3, afterUser.getLevel(), "应该升级到V3");
        System.out.println("✅ V3升级成功");
    }

    private void testLevelUpgrade_V4() {
        System.out.println("\n--- V4等级升级测试 ---");
        System.out.println("升级条件：个人质押20000U + 社区业绩30W + 3个V3直推");
        
        // 创建主用户
        User mainUser = createTestUser(2300L, 0, 0);
        StakeUser mainStakeUser = createTestStakeUser(2300L, 
            new BigDecimal("20000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(2300L, 0L, "/0");

        // 创建3个V3直推用户
        for (int i = 1; i <= 3; i++) {
            User directUser = createTestUser(2300L + i, 3, 0); // 设置为V3级别
            StakeUser directStakeUser = createTestStakeUser(2300L + i, 
                new BigDecimal("5000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(2300L + i, 2300L, "/0/2300/" + (2300L + i));
            
            // 为每个V3用户创建团队以满足V3的社区业绩要求（10W）
            for (int j = 1; j <= 25; j++) {
                User teamUser = createTestUser(2300L + i * 1000 + j, 0, 0);
                StakeUser teamStakeUser = createTestStakeUser(2300L + i * 1000 + j, 
                    new BigDecimal("4000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                createUserRelation(2300L + i * 1000 + j, 2300L + i, "/0/2300/" + (2300L + i) + "/" + (2300L + i * 1000 + j));
            }
        }

        // 创建额外团队用户以达到30W社区业绩
        for (int i = 1; i <= 50; i++) {
            User teamUser = createTestUser(2400L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(2400L + i, 
                new BigDecimal("4000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(2400L + i, 2300L, "/0/2300/" + (2400L + i));
        }

        System.out.println("升级前等级: V" + mainUser.getLevel());
        System.out.println("个人质押: " + mainStakeUser.getCurrentAmount());
        
        // 手动检查升级条件
        BigDecimal communityPerf = job30.getCommunityPerformance(2300L);
        System.out.println("社区业绩: " + communityPerf);
        
        // 检查直推用户
        List<Long> directUserIds = job30.getDirectUserIds(2300L);
        System.out.println("直推用户数量: " + directUserIds.size());
        System.out.println("直推用户ID: " + directUserIds);
        
        for (Long directUserId : directUserIds) {
            User directUser = userMapper.selectById(directUserId);
            StakeUser directStakeUser = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, directUserId)
            );
            BigDecimal directCommunityPerf = job30.getCommunityPerformance(directUserId);
            int directLevel = job30.getUserLevel(
                directStakeUser != null ? directStakeUser.getCurrentAmount() : BigDecimal.ZERO,
                directCommunityPerf,
                directUserId
            );
            System.out.println("直推用户" + directUserId + ": 当前等级V" + directUser.getLevel() + 
                ", 个人质押=" + (directStakeUser != null ? directStakeUser.getCurrentAmount() : "0") +
                ", 社区业绩=" + directCommunityPerf + ", 计算等级V" + directLevel);
        }
        
        // 执行结算
        job30.executeSettlementTransaction();

        // 验证升级结果
        User afterUser = userMapper.selectById(2300L);
        System.out.println("升级后等级: V" + afterUser.getLevel());
        
        // 如果升级失败，再次检查条件
        if (afterUser.getLevel() != 4) {
            System.out.println("V4升级失败，重新检查条件:");
            System.out.println("  个人质押20000U: " + mainStakeUser.getCurrentAmount() + " >= 20000? " + 
                (mainStakeUser.getCurrentAmount().compareTo(new BigDecimal("20000")) >= 0));
            System.out.println("  社区业绩30W: " + communityPerf + " >= 300000? " + 
                (communityPerf.compareTo(new BigDecimal("300000")) >= 0));
            
            int v3Count = 0;
            for (Long directUserId : directUserIds) {
                User directUser = userMapper.selectById(directUserId);
                if (directUser.getLevel() >= 3) {
                    v3Count++;
                }
            }
            System.out.println("  V3直推用户: " + v3Count + " >= 3? " + (v3Count >= 3));
        }
        
        assertEquals(4, afterUser.getLevel(), "应该升级到V4");
        System.out.println("✅ V4升级成功");
    }

    private void testLevelUpgrade_V5() {
        System.out.println("\n--- V5等级升级测试 ---");
        System.out.println("升级条件：个人质押50000U + 社区业绩100W + 3个V4直推");
        
        // 创建主用户
        User mainUser = createTestUser(2500L, 0, 0);
        StakeUser mainStakeUser = createTestStakeUser(2500L, 
            new BigDecimal("50000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(2500L, 0L, "/0");

        // 创建3个V4直推用户
        for (int i = 1; i <= 3; i++) {
            User directUser = createTestUser(2500L + i, 4, 0); // 设置为V4级别
            StakeUser directStakeUser = createTestStakeUser(2500L + i, 
                new BigDecimal("20000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(2500L + i, 2500L, "/0/2500/" + (2500L + i));
            
            // 为每个V4用户创建团队以满足V4的社区业绩要求（30W）
            for (int j = 1; j <= 75; j++) {
                User teamUser = createTestUser(2500L + i * 10000 + j, 0, 0);
                StakeUser teamStakeUser = createTestStakeUser(2500L + i * 10000 + j, 
                    new BigDecimal("4000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                createUserRelation(2500L + i * 10000 + j, 2500L + i, "/0/2500/" + (2500L + i) + "/" + (2500L + i * 10000 + j));
            }
        }

        // 创建额外团队用户以达到100W社区业绩
        for (int i = 1; i <= 100; i++) {
            User teamUser = createTestUser(2600L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(2600L + i, 
                new BigDecimal("5000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(2600L + i, 2500L, "/0/2500/" + (2600L + i));
        }

        System.out.println("升级前等级: V" + mainUser.getLevel());
        System.out.println("个人质押: " + mainStakeUser.getCurrentAmount());
        
        // 手动检查升级条件
        BigDecimal communityPerf = job30.getCommunityPerformance(2500L);
        System.out.println("社区业绩: " + communityPerf);
        
        // 检查直推用户
        List<Long> directUserIds = job30.getDirectUserIds(2500L);
        System.out.println("直推用户数量: " + directUserIds.size());
        System.out.println("直推用户ID: " + directUserIds);
        
        for (Long directUserId : directUserIds) {
            User directUser = userMapper.selectById(directUserId);
            StakeUser directStakeUser = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, directUserId)
            );
            BigDecimal directCommunityPerf = job30.getCommunityPerformance(directUserId);
            int directLevel = job30.getUserLevel(
                directStakeUser != null ? directStakeUser.getCurrentAmount() : BigDecimal.ZERO,
                directCommunityPerf,
                directUserId
            );
            System.out.println("直推用户" + directUserId + ": 当前等级V" + directUser.getLevel() + 
                ", 个人质押=" + (directStakeUser != null ? directStakeUser.getCurrentAmount() : "0") +
                ", 社区业绩=" + directCommunityPerf + ", 计算等级V" + directLevel);
        }
        
        // 执行结算
        job30.executeSettlementTransaction();

        // 验证升级结果
        User afterUser = userMapper.selectById(2500L);
        System.out.println("升级后等级: V" + afterUser.getLevel());
        
        // 如果升级失败，再次检查条件
        if (afterUser.getLevel() != 5) {
            System.out.println("V5升级失败，重新检查条件:");
            System.out.println("  个人质押50000U: " + mainStakeUser.getCurrentAmount() + " >= 50000? " + 
                (mainStakeUser.getCurrentAmount().compareTo(new BigDecimal("50000")) >= 0));
            System.out.println("  社区业绩100W: " + communityPerf + " >= 1000000? " + 
                (communityPerf.compareTo(new BigDecimal("1000000")) >= 0));
            
            int v4Count = 0;
            for (Long directUserId : directUserIds) {
                User directUser = userMapper.selectById(directUserId);
                if (directUser.getLevel() >= 4) {
                    v4Count++;
                }
            }
            System.out.println("  V4直推用户: " + v4Count + " >= 3? " + (v4Count >= 3));
        }
        
        assertEquals(5, afterUser.getLevel(), "应该升级到V5");
        System.out.println("✅ V5升级成功");
    }

    private void testLevelUpgrade_V6() {
        System.out.println("\n--- V6等级升级测试 ---");
        System.out.println("升级条件：个人质押100000U + 社区业绩300W + 3个V5直推");
        
        // 创建主用户
        User mainUser = createTestUser(2800L, 0, 0);
        StakeUser mainStakeUser = createTestStakeUser(2800L, 
            new BigDecimal("100000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(2800L, 0L, "/0");

        // 创建3个V5直推用户
        for (int i = 1; i <= 3; i++) {
            User directUser = createTestUser(2800L + i, 5, 0); // 设置为V5级别
            StakeUser directStakeUser = createTestStakeUser(2800L + i, 
                new BigDecimal("50000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(2800L + i, 2800L, "/0/2800/" + (2800L + i));
            
            // 为每个V5用户创建团队以满足V5的社区业绩要求（100W）
            for (int j = 1; j <= 200; j++) {
                User teamUser = createTestUser(2800L + i * 100000 + j, 0, 0);
                StakeUser teamStakeUser = createTestStakeUser(2800L + i * 100000 + j, 
                    new BigDecimal("5000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
                createUserRelation(2800L + i * 100000 + j, 2800L + i, "/0/2800/" + (2800L + i) + "/" + (2800L + i * 100000 + j));
            }
        }

        // 创建额外团队用户以达到300W社区业绩
        for (int i = 1; i <= 200; i++) {
            User teamUser = createTestUser(5000L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(5000L + i, 
                new BigDecimal("6000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(5000L + i, 2800L, "/0/2800/" + (5000L + i));
        }

        System.out.println("升级前等级: V" + mainUser.getLevel());
        System.out.println("个人质押: " + mainStakeUser.getCurrentAmount());
        
        // 手动检查升级条件
        BigDecimal communityPerf = job30.getCommunityPerformance(2800L);
        System.out.println("社区业绩: " + communityPerf);
        
        // 检查直推用户
        List<Long> directUserIds = job30.getDirectUserIds(2800L);
        System.out.println("直推用户数量: " + directUserIds.size());
        System.out.println("直推用户ID: " + directUserIds);
        
        for (Long directUserId : directUserIds) {
            User directUser = userMapper.selectById(directUserId);
            StakeUser directStakeUser = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, directUserId)
            );
            BigDecimal directCommunityPerf = job30.getCommunityPerformance(directUserId);
            int directLevel = job30.getUserLevel(
                directStakeUser != null ? directStakeUser.getCurrentAmount() : BigDecimal.ZERO,
                directCommunityPerf,
                directUserId
            );
            System.out.println("直推用户" + directUserId + ": 当前等级V" + directUser.getLevel() + 
                ", 个人质押=" + (directStakeUser != null ? directStakeUser.getCurrentAmount() : "0") +
                ", 社区业绩=" + directCommunityPerf + ", 计算等级V" + directLevel);
        }
        
        // 执行结算
        job30.executeSettlementTransaction();

        // 验证升级结果
        User afterUser = userMapper.selectById(2800L);
        System.out.println("升级后等级: V" + afterUser.getLevel());
        
        // 如果升级失败，再次检查条件
        if (afterUser.getLevel() != 6) {
            System.out.println("V6升级失败，重新检查条件:");
            System.out.println("  个人质押100000U: " + mainStakeUser.getCurrentAmount() + " >= 100000? " + 
                (mainStakeUser.getCurrentAmount().compareTo(new BigDecimal("100000")) >= 0));
            System.out.println("  社区业绩300W: " + communityPerf + " >= 3000000? " + 
                (communityPerf.compareTo(new BigDecimal("3000000")) >= 0));
            
            int v5Count = 0;
            for (Long directUserId : directUserIds) {
                User directUser = userMapper.selectById(directUserId);
                if (directUser.getLevel() >= 5) {
                    v5Count++;
                }
            }
            System.out.println("  V5直推用户: " + v5Count + " >= 3? " + (v5Count >= 3));
        }
        
        assertEquals(6, afterUser.getLevel(), "应该升级到V6");
        System.out.println("✅ V6升级成功");
    }

    @Test
    @DisplayName("测试用户等级升级：边界条件测试")
    void testUserLevelUpgrade_EdgeCases() {
        System.out.println("=== 用户等级升级边界条件测试 ===");
        
        // 测试个人质押不足的情况
        testLevelUpgrade_InsufficientPersonalStake();
        
        // 测试社区业绩不足的情况
        testLevelUpgrade_InsufficientCommunityPerformance();
        
        // 测试直推用户等级不足的情况
        testLevelUpgrade_InsufficientDirectUserLevel();
        
        System.out.println("✅ 用户等级升级边界条件测试全部通过");
    }

    private void testLevelUpgrade_InsufficientPersonalStake() {
        System.out.println("\n--- 个人质押不足测试 ---");
        
        // 创建用户，个人质押不足V1要求（需要100U，只有50U）
        User testUser = createTestUser(3100L, 0, 0);
        StakeUser testStakeUser = createTestStakeUser(3100L, 
            new BigDecimal("50"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(3100L, 0L, "/0");

        // 创建足够的团队用户满足社区业绩
        for (int i = 1; i <= 10; i++) {
            User teamUser = createTestUser(3100L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(3100L + i, 
                new BigDecimal("1000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(3100L + i, 3100L, "/0/3100");
        }

        System.out.println("个人质押: " + testStakeUser.getCurrentAmount() + " (不足V1要求的100U)");
        
        // 执行结算
        job30.executeSettlementTransaction();

        // 验证不会升级
        User afterUser = userMapper.selectById(3100L);
        assertEquals(0, afterUser.getLevel(), "个人质押不足时不应该升级");
        System.out.println("结果: 保持V0等级 ✅");
    }

    private void testLevelUpgrade_InsufficientCommunityPerformance() {
        System.out.println("\n--- 社区业绩不足测试 ---");
        
        // 创建用户，个人质押足够但社区业绩不足
        User testUser = createTestUser(3200L, 0, 0);
        StakeUser testStakeUser = createTestStakeUser(3200L, 
            new BigDecimal("100"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(3200L, 0L, "/0");

        // 创建少量团队用户，不足V1要求的1W社区业绩
        for (int i = 1; i <= 3; i++) {
            User teamUser = createTestUser(3200L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(3200L + i, 
                new BigDecimal("1000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(3200L + i, 3200L, "/0/3200");
        }

        System.out.println("个人质押: " + testStakeUser.getCurrentAmount() + " (满足V1要求)");
        System.out.println("社区业绩: 约3000U (不足V1要求的10000U)");
        
        // 执行结算
        job30.executeSettlementTransaction();

        // 验证不会升级
        User afterUser = userMapper.selectById(3200L);
        assertEquals(0, afterUser.getLevel(), "社区业绩不足时不应该升级");
        System.out.println("结果: 保持V0等级 ✅");
    }

    private void testLevelUpgrade_InsufficientDirectUserLevel() {
        System.out.println("\n--- 直推用户等级不足测试 ---");
        
        // 创建用户，尝试升级到V2但直推用户等级不足
        User testUser = createTestUser(3300L, 0, 0);
        StakeUser testStakeUser = createTestStakeUser(3300L, 
            new BigDecimal("1000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        createUserRelation(3300L, 0L, "/0");

        // 创建2个V0直推用户（V2需要2个V1直推）
        for (int i = 1; i <= 2; i++) {
            User directUser = createTestUser(3300L + i, 0, 0); // V0级别，不足V2要求
            StakeUser directStakeUser = createTestStakeUser(3300L + i, 
                new BigDecimal("100"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(3300L + i, 3300L, "/0/3300");
        }

        // 创建足够的团队用户满足社区业绩
        for (int i = 1; i <= 30; i++) {
            User teamUser = createTestUser(3400L + i, 0, 0);
            StakeUser teamStakeUser = createTestStakeUser(3400L + i, 
                new BigDecimal("1000"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            createUserRelation(3400L + i, 3300L, "/0/3300");
        }

        System.out.println("个人质押: " + testStakeUser.getCurrentAmount() + " (满足V2要求)");
        System.out.println("直推用户: 2个V0用户 (V2需要2个V1用户)");
        
        // 执行结算
        job30.executeSettlementTransaction();

        // 验证只能升级到V1，不能升级到V2
        User afterUser = userMapper.selectById(3300L);
        assertEquals(1, afterUser.getLevel(), "直推用户等级不足时只能升级到V1");
        System.out.println("结果: 升级到V1等级 ✅");
    }
}