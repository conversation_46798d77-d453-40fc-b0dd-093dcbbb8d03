package com.aic.app.job;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.aic.app.service.RustService;
import com.aic.app.util.Utils;

import jakarta.annotation.Resource;

@SpringBootTest
public class WithdrawJobTest {
    
    @Resource
    WithdrawJob withdrawJob;

    @Test
    public void testRun() {
        // This will start the WithdrawJob and run it in a separate thread
        withdrawJob.execute();
        // Allow some time for the job to run
    }

}
