package com.aic.app.api;

import com.aic.app.form.WithdrawUnifiedForm;
import com.aic.app.model.User;
import com.aic.app.model.LockOrder;
import com.aic.app.model.StakeUser;
import com.aic.app.service.IUserService;
import com.aic.app.service.ILockOrderService;
import com.aic.app.service.IStakeUserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 统一提取API测试
 */
@SpringBootTest
@Transactional
public class WithdrawApiTest {

    @Resource
    private UserController userController;
    
    @MockBean
    private ILockOrderService lockOrderService;
    
    @MockBean
    private IStakeUserService stakeUserService;
    
    @Resource
    private IUserService userService;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testWithdrawLockOrderRelease() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();
        
        // 创建测试用户
        User testUser = createTestUser();
        
        // Mock锁仓服务返回
        when(lockOrderService.withdrawLockOrderRelease(eq(testUser.getId()), eq(1L), any(BigDecimal.class)))
            .thenReturn(new BigDecimal("50"));
        
        // 创建请求表单
        WithdrawUnifiedForm form = new WithdrawUnifiedForm();
        form.setType(0); // 提取锁仓释放
        form.setAmount(new BigDecimal("50"));
        form.setId(1L);
        
        
        // 执行请求
        mockMvc.perform(post("/api/user/withdraw")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form))
                .requestAttr("user", testUser))
                .andExpect(status().isOk())
                .andDo(print())
                ;
        
        System.out.println("锁仓释放提取API测试通过");
    }

    @Test
    public void testWithdrawStakeStaticPool() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();
        
        // 创建测试用户
        User testUser = createTestUser();
        
        // Mock质押服务返回
        when(stakeUserService.withdrawStakeStaticPool(eq(testUser.getId()), eq("XYC"), any(BigDecimal.class)))
            .thenReturn(new BigDecimal("30"));
        
        // 创建请求表单
        WithdrawUnifiedForm form = new WithdrawUnifiedForm();
        form.setType(1); // 提取质押静态池
        form.setAmount(new BigDecimal("30"));
        
        
        // 执行请求
        mockMvc.perform(post("/api/user/withdraw")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form))
                .requestAttr("user", testUser))
                .andExpect(status().isOk())
                ;
        
        System.out.println("质押静态池提取API测试通过");
    }

    @Test
    public void testWithdrawLockStaticPool() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();

        // 创建测试用户
        User testUser = createTestUser();

        // Mock质押服务返回
        when(stakeUserService.withdrawLockStaticPool(eq(testUser.getId()), eq("XYC"), any(BigDecimal.class)))
            .thenReturn(new BigDecimal("40"));

        // 创建请求表单
        WithdrawUnifiedForm form = new WithdrawUnifiedForm();
        form.setType(3); // 提取锁仓静态池
        form.setAmount(new BigDecimal("40"));
        

        // 执行请求
        mockMvc.perform(post("/api/user/withdraw")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form))
                .requestAttr("user", testUser))
                .andExpect(status().isOk())
                ;

        System.out.println("锁仓静态池提取API测试通过");
    }

    @Test
    public void testWithdrawLockDynamicPool() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();

        // 创建测试用户
        User testUser = createTestUser();

        // Mock质押服务返回
        when(stakeUserService.withdrawLockDynamicPool(eq(testUser.getId()), eq("XYC"), any(BigDecimal.class)))
            .thenReturn(new BigDecimal("25"));

        // 创建请求表单
        WithdrawUnifiedForm form = new WithdrawUnifiedForm();
        form.setType(4); // 提取锁仓动态池
        form.setAmount(new BigDecimal("25"));
        

        // 执行请求
        mockMvc.perform(post("/api/user/withdraw")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form))
                .requestAttr("user", testUser))
                .andExpect(status().isOk())
                ;

        System.out.println("锁仓动态池提取API测试通过");
    }

    @Test
    public void testWithdrawInvalidType() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();
        
        // 创建测试用户
        User testUser = createTestUser();
        
        // 创建无效类型的请求表单
        WithdrawUnifiedForm form = new WithdrawUnifiedForm();
        form.setType(99); // 无效类型
        form.setAmount(new BigDecimal("30"));
        
        
        // 执行请求，期望返回错误
        mockMvc.perform(post("/api/user/withdraw")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form))
                .requestAttr("user", testUser))
                .andExpect(status().isBadRequest()); // 验证参数应该失败
        
        System.out.println("无效类型提取API测试通过");
    }

    private User createTestUser() {
        User user = new User();
        user.setId(1L);
        user.setAddress("0x1234567890123456789012345678901234567890");
        user.setType(1);
        return user;
    }
}
