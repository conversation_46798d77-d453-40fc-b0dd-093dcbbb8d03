package com.aic.app.service;

import com.aic.app.form.WithdrawNormalProductForm;
import com.aic.app.model.User;
import com.aic.app.model.UserProduct;
import com.aic.app.model.Withdraw;
import com.aic.app.service.RustService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 普通商品提取功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class NormalProductWithdrawTest {

    @Resource
    private IUserProductService userProductService;
    
    @Resource
    private IUserService userService;
    
    @Resource
    private IWithdrawService withdrawService;

    /**
     * 创建测试用户
     */
    private User createTestUser() {
        User user = new User();
        user.setId(20001L);
        user.setAddress("0xTestAddress123456789");
        user.setCode("TEST" + System.currentTimeMillis());
        user.setType(1); // 钱包用户
        user.setCreateTime(new Date());
        
        userService.save(user);
        return user;
    }

    /**
     * 创建有可提取金额的普通商品订单
     */
    private UserProduct createTestNormalProductWithAvailableAmount(Long userId) {
        UserProduct userProduct = new UserProduct();
        userProduct.setUserId(userId);
        userProduct.setOrderNo("WITHDRAW_TEST" + System.currentTimeMillis());
        userProduct.setProductId(999L);
        userProduct.setType(2); // 普通商品
        userProduct.setAmount(new BigDecimal("3600")); // 总金额3600
        userProduct.setPrice(new BigDecimal("100"));
        userProduct.setQuantity(new BigDecimal(36));
        userProduct.setStatus(1); // 有效状态
        userProduct.setDay(360);
        userProduct.setReleaseDay(0);
        userProduct.setRate(new BigDecimal("0.004"));
        userProduct.setCreateTime(new Date());
        
        // 设置线性释放相关字段
        userProduct.setDailyReleaseAmount(new BigDecimal("10")); // 每日释放10
        userProduct.setTotalReleased(new BigDecimal("100")); // 已释放100
        userProduct.setAvailableAmount(new BigDecimal("100")); // 可提取100
        userProduct.setLastReleaseDate(new Date());
        
        userProductService.save(userProduct);
        
        System.out.println("创建测试订单: " + userProduct.getOrderNo() + 
                          ", 可提取金额: " + userProduct.getAvailableAmount());
        
        return userProduct;
    }
}
