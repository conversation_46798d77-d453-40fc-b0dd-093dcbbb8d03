package com.aic.app.service;

import com.aic.app.model.StakeUser;
import com.aic.app.model.User;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

/**
 * 锁仓静态池和动态池提取功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class WithdrawLockPoolTest {

    @Resource
    private IStakeUserService stakeUserService;
    
    @Resource
    private IUserService userService;

    @Test
    public void testWithdrawLockStaticPool() {
        System.out.println("=== 测试锁仓静态池提取功能 ===");
        
        // 创建测试用户
        User testUser = createTestUser();
        
        // 创建测试质押用户记录
        StakeUser testStakeUser = createTestStakeUser(testUser.getId());
        
        try {
            // 执行提取
            BigDecimal withdrawAmount = new BigDecimal("30");
            BigDecimal result = stakeUserService.withdrawLockStaticPool(
                testUser.getId(), "XYC", withdrawAmount);
            
            System.out.println("锁仓静态池提取成功，金额: " + result);
            
        } catch (Exception e) {
            System.out.println("锁仓静态池提取失败: " + e.getMessage());
        }
    }

    @Test
    public void testWithdrawLockDynamicPool() {
        System.out.println("=== 测试锁仓动态池提取功能 ===");
        
        // 创建测试用户
        User testUser = createTestUser();
        
        // 创建测试质押用户记录
        StakeUser testStakeUser = createTestStakeUser(testUser.getId());
        
        try {
            // 执行提取
            BigDecimal withdrawAmount = new BigDecimal("20");
            BigDecimal result = stakeUserService.withdrawLockDynamicPool(
                testUser.getId(), "XYC", withdrawAmount);
            
            System.out.println("锁仓动态池提取成功，金额: " + result);
            
        } catch (Exception e) {
            System.out.println("锁仓动态池提取失败: " + e.getMessage());
        }
    }

    private User createTestUser() {
        User user = new User();
        user.setAddress("0x" + System.currentTimeMillis());
        user.setType(1);
        userService.save(user);
        return user;
    }

    private StakeUser createTestStakeUser(Long userId) {
        StakeUser stakeUser = new StakeUser();
        stakeUser.setUserId(userId);
        stakeUser.setTokenId("XYC");
        stakeUser.setCurrentAmount(new BigDecimal("1000"));
        stakeUser.setStaticPool(new BigDecimal("100"));
        stakeUser.setDynamicPool(new BigDecimal("50"));
        stakeUser.setLockStaticPool(new BigDecimal("80"));  // 设置锁仓静态池余额
        stakeUser.setLockDynamicPool(new BigDecimal("40")); // 设置锁仓动态池余额
        stakeUserService.save(stakeUser);
        return stakeUser;
    }
}
