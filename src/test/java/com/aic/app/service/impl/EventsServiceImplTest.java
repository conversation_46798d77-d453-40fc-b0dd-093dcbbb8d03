package com.aic.app.service.impl;

import com.aic.app.model.*;
import com.aic.app.service.*;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidPooledConnection;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class EventsServiceImplTest {

    @Resource
    EventsServiceImpl eventsService;

    @Resource
    IStakeUserService stakeUserService;

    @Resource
    IProductService productService;

    @Resource
    ILockOrderService lockOrderService;

    @Resource
    IUserService userService;


    @Test
    // @Transactional
    void handleEvent() {
        // 提取静态
//        Events events = eventsService.getById(11);
//        eventsService.handleEvent(events);
//        List<Events> events = eventsService.list(new LambdaQueryWrapper<Events>().eq(Events::getStatus, 0).last("limit 2"));
        // List<Events> events = eventsService.list(new LambdaQueryWrapper<Events>().eq(Events::getStatus, 0).in(BaseEntity::getId,37 ).last("limit 2"));
        List<Events> events = eventsService.list(new LambdaQueryWrapper<Events>().eq(Events::getStatus, 0).in(BaseEntity::getId,88 ).last("limit 2"));
        events.forEach(eventsService::handleEvent);

    }

    @Test
    @Transactional
    void testBuyInfoEvent() {
        String testAddress = "******************************************";
        Integer productId = 7;

        // 1. 创建BuyInfo事件测试数据
        Events buyInfoEvent = new Events();
        buyInfoEvent.setName("BuyInfo");
        buyInfoEvent.setTxid("0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef");
        buyInfoEvent.setContract("******************************************");
        buyInfoEvent.setHeight(12345L);
        buyInfoEvent.setStatus(0);

        // 创建事件数据
        Events.Data data = new Events.Data();
        data.setTxid("0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef");
        data.setAddress(testAddress);
        data.setContract("******************************************");
        data.setPid(productId);
        data.setAmount("2");  // 购买2个商品
        data.setPrice("12000.20000000");  // 产品价格
        data.setValue("24000.40000000");  // 总价值 = 价格 * 数量 = 12000.20 * 2

        buyInfoEvent.setData(data);

        // 保存事件到数据库
        eventsService.save(buyInfoEvent);

        // 处理BuyInfo事件
        eventsService.handleEvent(buyInfoEvent);

        // 验证事件状态已更新为已处理
        Events processedEvent = eventsService.getById(buyInfoEvent.getId());
        assertEquals(1, processedEvent.getStatus());

        System.out.println("BuyInfo事件处理测试完成");
    }

    @Test
    @Transactional
    void testBuyInfoEventProduct6ThenProduct2() {
        String testAddress = "******************************************";

        // 1. 先购买产品6（预售节点，day=6）
        Events buyInfoEvent6 = new Events();
        buyInfoEvent6.setName("BuyInfo");
        buyInfoEvent6.setTxid("0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890");
        buyInfoEvent6.setContract("******************************************");
        buyInfoEvent6.setHeight(12346L);
        buyInfoEvent6.setStatus(0);

        Events.Data data6 = new Events.Data();
        data6.setTxid("0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890");
        data6.setAddress(testAddress);
        data6.setContract("******************************************");
        data6.setPid(6);
        data6.setAmount("1");
        data6.setPrice("100000.00000000");
        data6.setValue("100000.00000000");

        buyInfoEvent6.setData(data6);
        eventsService.save(buyInfoEvent6);
        eventsService.handleEvent(buyInfoEvent6);

        // 验证第一个事件处理完成
        Events processedEvent6 = eventsService.getById(buyInfoEvent6.getId());
        assertEquals(1, processedEvent6.getStatus());

        // 查询用户并打印preLevel
        var user = stakeUserService.checkUser(testAddress);
        System.out.println("购买产品6后，用户preLevel = " + user.getPreLevel());

        // 2. 再购买产品2（预售节点，day=2）
        Events buyInfoEvent2 = new Events();
        buyInfoEvent2.setName("BuyInfo");
        buyInfoEvent2.setTxid("0xfedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321");
        buyInfoEvent2.setContract("******************************************");
        buyInfoEvent2.setHeight(12347L);
        buyInfoEvent2.setStatus(0);

        Events.Data data2 = new Events.Data();
        data2.setTxid("0xfedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321");
        data2.setAddress(testAddress);
        data2.setContract("******************************************");
        data2.setPid(2);
        data2.setAmount("1");
        data2.setPrice("1000.00000000");
        data2.setValue("1000.00000000");

        buyInfoEvent2.setData(data2);
        eventsService.save(buyInfoEvent2);
        eventsService.handleEvent(buyInfoEvent2);

        // 验证第二个事件处理完成
        Events processedEvent2 = eventsService.getById(buyInfoEvent2.getId());
        assertEquals(1, processedEvent2.getStatus());

        // 再次查询用户并打印preLevel
        user = stakeUserService.checkUser(testAddress);
        System.out.println("购买产品2后，用户preLevel = " + user.getPreLevel());

        System.out.println("连续购买产品6和产品2的测试完成");
    }

    @Test
    @Transactional
    void testBuyInfoEventProduct2ThenProduct6() {
        String testAddress = "******************************************";

        // 1. 先购买产品2（预售节点，day=2）
        Events buyInfoEvent2 = new Events();
        buyInfoEvent2.setName("BuyInfo");
        buyInfoEvent2.setTxid("0x1111111111111111111111111111111111111111111111111111111111111111");
        buyInfoEvent2.setContract("******************************************");
        buyInfoEvent2.setHeight(12348L);
        buyInfoEvent2.setStatus(0);

        Events.Data data2 = new Events.Data();
        data2.setTxid("0x1111111111111111111111111111111111111111111111111111111111111111");
        data2.setAddress(testAddress);
        data2.setContract("******************************************");
        data2.setPid(2);
        data2.setAmount("1");
        data2.setPrice("1000.00000000");
        data2.setValue("1000.00000000");

        buyInfoEvent2.setData(data2);
        eventsService.save(buyInfoEvent2);
        eventsService.handleEvent(buyInfoEvent2);

        // 验证第一个事件处理完成
        Events processedEvent2 = eventsService.getById(buyInfoEvent2.getId());
        assertEquals(1, processedEvent2.getStatus());

        // 查询用户并打印preLevel
        var user = stakeUserService.checkUser(testAddress);
        System.out.println("购买产品2后，用户preLevel = " + user.getPreLevel());

        // 2. 再购买产品6（预售节点，day=6）
        Events buyInfoEvent6 = new Events();
        buyInfoEvent6.setName("BuyInfo");
        buyInfoEvent6.setTxid("0x2222222222222222222222222222222222222222222222222222222222222222");
        buyInfoEvent6.setContract("******************************************");
        buyInfoEvent6.setHeight(12349L);
        buyInfoEvent6.setStatus(0);

        Events.Data data6 = new Events.Data();
        data6.setTxid("0x2222222222222222222222222222222222222222222222222222222222222222");
        data6.setAddress(testAddress);
        data6.setContract("******************************************");
        data6.setPid(6);
        data6.setAmount("1");
        data6.setPrice("100000.00000000");
        data6.setValue("100000.00000000");

        buyInfoEvent6.setData(data6);
        eventsService.save(buyInfoEvent6);
        eventsService.handleEvent(buyInfoEvent6);

        // 验证第二个事件处理完成
        Events processedEvent6 = eventsService.getById(buyInfoEvent6.getId());
        assertEquals(1, processedEvent6.getStatus());

        // 再次查询用户并打印preLevel
        user = stakeUserService.checkUser(testAddress);
        System.out.println("购买产品6后，用户preLevel = " + user.getPreLevel());

        System.out.println("连续购买产品2和产品6的测试完成");
    }

    @Test
    @Transactional
    void testBuyInfoEventProduct1001() {
        String testAddress = "******************************************";
        Integer productId = 1001;

        // 1. 创建BuyInfo事件测试数据 - 产品ID 1001（债券产品）
        Events buyInfoEvent = new Events();
        buyInfoEvent.setName("BuyInfo");
        buyInfoEvent.setTxid("0x3333333333333333333333333333333333333333333333333333333333333333");
        buyInfoEvent.setContract("******************************************");
        buyInfoEvent.setHeight(12350L);
        buyInfoEvent.setStatus(0);

        // 创建事件数据
        Events.Data data = new Events.Data();
        data.setTxid("0x3333333333333333333333333333333333333333333333333333333333333333");
        data.setAddress(testAddress);
        data.setContract("******************************************");
        data.setPid(productId);
        data.setAmount("5");  // 购买5个债券产品
        data.setPrice("100.00000000");  // 产品价格
        data.setValue("500.00000000");  // 总价值 = 价格 * 数量 = 100.00 * 5

        buyInfoEvent.setData(data);

        // 保存事件到数据库
        eventsService.save(buyInfoEvent);

        // 处理BuyInfo事件
        eventsService.handleEvent(buyInfoEvent);

        // 验证事件状态已更新为已处理
        Events processedEvent = eventsService.getById(buyInfoEvent.getId());
        assertEquals(1, processedEvent.getStatus());

        // 查询用户并打印preLevel（债券产品不应该更新preLevel）
        var user = stakeUserService.checkUser(testAddress);
        System.out.println("购买产品1001后，用户preLevel = " + user.getPreLevel());

        System.out.println("BuyInfo事件（产品ID 1001 - 债券产品）处理测试完成");
    }

    // test 购买产品2
    @Test
    @Transactional
    void testBuyInfoEventProduct2() {
        String testAddress = "******************************************";
        Integer productId = 2;

        // 1. 创建BuyInfo事件测试数据 - 产品ID 2（普通商品）
        Events buyInfoEvent = new Events();
        buyInfoEvent.setName("BuyInfo");
        buyInfoEvent.setTxid("0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890");
        buyInfoEvent.setContract("******************************************");
        buyInfoEvent.setHeight(12346L);
        buyInfoEvent.setStatus(0);

        // 创建事件数据
        Events.Data data = new Events.Data();
        data.setTxid("0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890");
        data.setAddress(testAddress);
        data.setContract("******************************************");
        data.setPid(productId);
        data.setAmount("1");  // 购买1个普通商品
        data.setPrice("1000.00000000");  // 产品价格
        data.setValue("1000.00000000");  // 总价值 = 价格 * 数量 = 1000.00 * 1

        buyInfoEvent.setData(data);

        // 保存事件到数据库
        eventsService.save(buyInfoEvent);

        // 处理BuyInfo事件
        eventsService.handleEvent(buyInfoEvent);

        // 验证事件状态已更新为已处理
        Events processedEvent = eventsService.getById(buyInfoEvent.getId());
        assertEquals(1, processedEvent.getStatus());

        // 验证锁仓订单锁仓数量是总价值/10U
        LockOrder lockOrder = lockOrderService.getOne(new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getSourceType, 1)
                .orderByDesc(LockOrder::getId)
                .last("limit 1"));
        // eq bigDecimal
        assertEquals(0, lockOrder.getLockAmount().compareTo(new BigDecimal("100")));

        System.out.println("BuyInfo事件（产品ID 2 - 普通商品）处理测试完成");
    }



    @Test
    @Transactional
    void testUnStakeEvent() {
        String testAddress = "0xb37bfff28579a1aaecdc933da0b6e8305e76cf2a";
        String tokenId = "XYC";

        // 1. 先确保用户存在并创建质押记录
        var user = stakeUserService.checkUser(testAddress);
        StakeUser stakeUser = stakeUserService.getStakeUser(user.getId(), tokenId);

        // 如果没有质押记录，创建一个
        if (stakeUser == null || stakeUser.getId() == null) {
            stakeUser = new StakeUser();
            stakeUser.setUserId(user.getId());
            stakeUser.setTokenId(tokenId);
            stakeUser.setCurrentAmount(new BigDecimal("10.000000000000000000"));
            stakeUser.setPendingAmount(new BigDecimal("5.000000000000000000"));
            stakeUser.setStaticPool(BigDecimal.ZERO);
            stakeUser.setDynamicPool(BigDecimal.ZERO);
            stakeUser.setTodayStatic(BigDecimal.ZERO);
            stakeUser.setTotalStatic(BigDecimal.ZERO);
            stakeUser.setCanReceive(BigDecimal.ZERO);
            stakeUser.setTodayDynamic(BigDecimal.ZERO);
            stakeUser.setTotalDynamic(BigDecimal.ZERO);
            stakeUser.setNodeReward(BigDecimal.ZERO);
            stakeUser.setNodePool(BigDecimal.ZERO);
            stakeUser.setSumAmount(BigDecimal.ZERO);
            stakeUserService.save(stakeUser);
        }

        // 2. 创建UnStake事件测试数据
        Events unStakeEvent = new Events();
        unStakeEvent.setName("UnStake");
        unStakeEvent.setTxid("0x9cec95a918ad17d74c1304469e8bc3fda2c579e99ba8b838c86f0741fc013107");
        unStakeEvent.setContract("******************************************");
        unStakeEvent.setHeight(12345L);
        unStakeEvent.setStatus(0);

        // 创建事件数据
        Events.Data data = new Events.Data();
        data.setTxid("0x9cec95a918ad17d74c1304469e8bc3fda2c579e99ba8b838c86f0741fc013107");
        data.setAddress(testAddress);
        data.setContract("******************************************");
        data.setStakeAmount("3.000000000000000000");  // 解质押3个token

        unStakeEvent.setData(data);

        // 保存事件到数据库
        eventsService.save(unStakeEvent);

        // 处理UnStake事件
        eventsService.handleEvent(unStakeEvent);

        // 验证事件状态已更新为已处理
        Events processedEvent = eventsService.getById(unStakeEvent.getId());
        assertEquals(1, processedEvent.getStatus());

        System.out.println("UnStake事件处理测试完成");
    }


    @Test
    @Transactional
    public void testBuy2() throws Exception {
        User user = userService.getById(1L);
        for (int i = 0; i < 10; i++) {
            Product product = productService.getById(1L);
            Events.Data data = new Events.Data();
            data.setPrice("100");
            data.setAmount("1");
            eventsService.handleType1Product(user, product, data);
        }
        for (int i = 0; i < 4; i++) {
            Product product = productService.getById(2L);
            Events.Data data = new Events.Data();
            data.setPrice("1000");
            data.setAmount("1");
            eventsService.handleType1Product(user, product, data);
            // 这里可以添加更多的测试逻辑
        }
    }
}
