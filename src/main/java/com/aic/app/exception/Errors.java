package com.aic.app.exception;

public interface Errors {

    BizException LOGIN_EXCEPTION = new BizException(201, "登录失败，请稍后再试");

    BizException AUTH_EXCEPTION = new BizException(401, "need login");
    
    BizException REQUEST_EXCEPTION = new BizException(110, "请求参数错误");
    
    BizException INVITE_CODE_EXCEPTION = new BizException(100, "邀请码错误");
    
    BizException LIMIT_AMOUNT_EXCEPTION = new BizException(101, "购买失败，额度不足");
    
    BizException INVITE_CODE_EXCEPTION2 = new BizException(103, "邀请码不能重复绑定");
    
    BizException SERVER_EXCEPTION = new BizException(500, "网络超时，请稍后再试");
    BizException WITHDRAW_BALANCE_EXCEPTION = new BizException(211, "提取失败，余额不足");
    
    // 提现相关错误码
    BizException WITHDRAW_AMOUNT_INVALID = new BizException(212, "提取金额必须大于0");
    BizException WITHDRAW_USER_RECORD_NOT_FOUND = new BizException(213, "用户质押记录不存在");
    BizException WITHDRAW_STATIC_POOL_INSUFFICIENT = new BizException(214, "静态池余额不足");
    BizException WITHDRAW_DYNAMIC_POOL_INSUFFICIENT = new BizException(215, "动态池余额不足");
    BizException WITHDRAW_LOCK_STATIC_POOL_INSUFFICIENT = new BizException(216, "锁仓静态池余额不足");
    BizException WITHDRAW_LOCK_DYNAMIC_POOL_INSUFFICIENT = new BizException(217, "锁仓动态池余额不足");
    BizException WITHDRAW_OPERATION_FAILED = new BizException(218, "提取操作失败");
    BizException WITHDRAW_TYPE_UNSUPPORTED = new BizException(219, "不支持的提取类型");
    BizException WITHDRAW_LOCK_ORDER_ID_REQUIRED = new BizException(220, "锁仓记录ID不能为空");
    
    // 锁仓订单相关错误码
    BizException LOCK_ORDER_NOT_FOUND = new BizException(221, "锁仓订单不存在");
    BizException LOCK_ORDER_PERMISSION_DENIED = new BizException(222, "无权限操作此锁仓订单");
    BizException LOCK_ORDER_STATUS_INVALID = new BizException(223, "锁仓订单状态异常");
    BizException LOCK_ORDER_AVAILABLE_INSUFFICIENT = new BizException(224, "可提取金额不足");

}
