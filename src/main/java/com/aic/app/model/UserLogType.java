package com.aic.app.model;

import lombok.Getter;

/**
 * 流水类型
 */
@Getter
public enum UserLogType {

    Stake("质押", 2),

    StakeProfit("质押收益", 3),

    RedeemCurrent("赎回质押", 5),

    RECEIVE("提取收益", 7),

    // 质押动态奖励细分类型
    STAKE_SHARE_REWARD("质押分享奖励", 47),
    STAKE_COMMUNITY_REWARD("质押社区奖励", 48),
    STAKE_PEER_LEVEL_REWARD("质押平级奖励", 49),
    
    // 锁仓释放相关
    LOCK_RELEASE_LINEAR("线性释放", 201),

    // 锁仓状态变更
    LOCK_ORDER_CREATE("锁仓订单创建", 221),

    // 产品购买相关
    PRODUCT_PURCHASE("购买产品", 230),
    
    // 城主商品相关
    CITY_LORD_PURCHASE("购买城主商品", 231),
    QUADRUPLE_REWARD_ADD("增加4倍收益次数", 232);

    final String label;
    final int value;

    UserLogType(String label, int value) {
        this.label = label;
        this.value = value;
    }
    
}
