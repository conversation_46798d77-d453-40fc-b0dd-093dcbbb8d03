package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用户理财产品表
 * @TableName user_stake
 */
@TableName(value ="user_stake")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class UserStake extends BaseEntity {
    /**
     * 类型：0-质押 1-解除质押
     */
    @Schema(description = "类型：0-质押 1-解除质押")
    private Integer type;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * TOKEN
     */
    @Schema(description = "TOKEN")
    private String tokenId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private BigDecimal quantity;

    /**
     * USD金额
     */
    @Schema(description = "USD金额")
    private BigDecimal usdAmount;

    /**
     * 支付方式
     */
    private String payToken;

    /**
     * 支付数量
     */
    private BigDecimal oriAmount;

    /**
     * 交易号
     */
    @Schema(description = "交易号")
    private String txid;

    /**
     * 0-待确认 1-已确认
     */
    @Schema(description = "0-待确认 1-已确认")
    private Integer status;
    
    private transient String address;
    private transient String code;


    public UserStake(Integer type, Long userId, String tokenId, BigDecimal quantity) {
        this.type = type;
        this.userId = userId;
        this.tokenId = tokenId;
        this.quantity = quantity;
        this.setCreateTime(new Date());
        this.status = 0;
    }

}
