package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public record UserLogQuery(@Schema(description = "类型：2-质押, 3-质押收益, 5-赎回质押, 7-提取收益, 47-质押分享奖励, 48-质押社区奖励, 49-质押平级奖励, 201-线性释放, 221-锁仓订单创建, 230-购买产品, 231-购买城主商品, 232-增加4倍收益次数") Integer[] type,
                           @DateTimeFormat(
                                   pattern = "yyyy-MM-dd"
                           )
                           @Schema(description = "开始日期") Date startDate,
                           @DateTimeFormat(
                                   pattern = "yyyy-MM-dd"
                           )
                           @Schema(description = "结束日期") Date endDate,
                           @Schema(description = "页数") Integer page,
                           @Schema(description = "tokenId") String tokenId,
                           @Schema(description = "symbol") String symbol,
                           @Schema(description = "每页页数：默认10") Integer size) {
}
