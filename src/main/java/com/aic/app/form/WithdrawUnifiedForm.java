package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.math.BigDecimal;

@Data
public class WithdrawUnifiedForm {

    @Schema(description = "提取类型：0-提取锁仓释放, 1-提取质押静态池, 2-提取质押动态池, 3-提取锁仓静态池, 4-提取锁仓动态池", required = true)
    @NotNull(message = "提取类型不能为空")
    @Min(value = 0, message = "提取类型必须在0-4之间")
    @Max(value = 4, message = "提取类型必须在0-4之间")
    private Integer type;

    @Schema(description = "提取金额", required = true)
    @NotNull(message = "提取金额不能为空")
    @Positive(message = "提取金额不能小于0")
    private BigDecimal amount;

    @Schema(description = "锁仓记录ID，仅type=0时需要")
    private Long id;

    // @Schema(description = "TOKEN ID", example = "XYC")
    private String tokenId = "XYC";
}
