package com.aic.app.vo;

import com.aic.app.model.UserLog;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserLogVo {

    @Schema(description = "ID")
    private int id;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "用户地址")
    private String address;
    @Schema(description = "类型：2-质押, 3-质押收益, 5-赎回质押, 7-提取收益, 47-质押分享奖励, 48-质押社区奖励, 49-质押平级奖励, 201-线性释放, 221-锁仓订单创建, 230-购买产品, 231-购买城主商品, 232-增加4倍收益次数")
    private int type;
    @Schema(description = "理财数量")
    private BigDecimal productAmount;
    @Schema(description = "收益/购买数量")
    private BigDecimal amount;

    @Schema(description = "参与时间: 格式 yyyy-MM-dd HH:mm:ss", example = "2024-08-06 21:20:21")
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    
    private String remark;
    private String symbol;
    private String code;

    public UserLogVo(UserLog userLog) {
        BeanUtils.copyProperties(userLog, this);
    }
}
