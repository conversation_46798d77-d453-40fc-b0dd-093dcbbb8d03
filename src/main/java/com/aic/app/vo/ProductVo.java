package com.aic.app.vo;

import com.aic.app.model.Product;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

@Data
public class ProductVo {

    private Long id;

    @Schema(description = "产品类型，1-预售节点 2-普通商品 3-债券")
    private int type;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "产品描述")
    private String description;

    @Schema(description = "收益率,显示的时候需要乘100")
    private BigDecimal rate;

    @Schema(description = "手续费")
    private BigDecimal fee;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "天数")
    private int day;
    
    @Schema(description = "图片")
    private String image;

    /**
     * 已售数量
     */
    @Schema(description = "已售数量")
    private BigDecimal sold;

    /**
     * 折扣率
     */
    
    @Schema(description = "已售数量")
    private BigDecimal discount;

    /**
     * 限制时间段 (格式: "10:00-13:00", 为空表示全天可抢购)
     */
    @Schema(description = "限制时间段 (格式: \"10:00-13:00\", 为空表示全天可抢购)")
    private String limitTime;

    private Boolean enable;

    // 剩余数量
    @Schema(description = "剩余数量")
    private BigDecimal remain;
    // 总量
    @Schema(description = "总量")
    private BigDecimal total;

    public ProductVo(Product row) {
        this.id = row.getId();
        this.type = row.getType();
        this.name = row.getName();
        this.description = row.getDescription();
        this.rate = row.getRate();
        this.fee = row.getFee();
        this.day = row.getDay();
        this.price = row.getPrice();
        this.image = row.getImage();
        this.enable = row.getEnable();
        this.total = row.getTotal();
        this.remain = Optional.ofNullable(row.getTotal()).orElse(BigDecimal.ZERO)
                .subtract(Optional.ofNullable(row.getSold()).orElse(BigDecimal.ZERO));
        if (this.remain.compareTo(BigDecimal.ZERO) < 0) {
            this.remain = BigDecimal.ZERO;
        }
        this.discount = Optional.ofNullable(row.getDiscount()).orElse(BigDecimal.ZERO);
        this.sold = row.getSold();
    }
}
