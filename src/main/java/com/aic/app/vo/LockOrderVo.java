package com.aic.app.vo;

import com.aic.app.model.LockOrder;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "锁仓订单信息")
@NoArgsConstructor
public class LockOrderVo {
    
    @Schema(description = "订单ID")
    private Long id;
    
    @Schema(description = "锁仓订单号")
    private String orderNo;
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "用户地址")
    private String address;
    
    @Schema(description = "来源类型: 1-节点产品 2-普通商品 3-债券产品 4-质押 5-其他")
    private Integer sourceType;
    
    @Schema(description = "来源ID")
    private Long sourceId;
    
    @Schema(description = "代币ID")
    private String tokenId;
    
    @Schema(description = "锁仓总金额")
    private BigDecimal lockAmount;
    
    @Schema(description = "已释放金额")
    private BigDecimal releasedAmount;
    
    @Schema(description = "可提取金额")
    private BigDecimal availableAmount;
    
    @Schema(description = "总锁仓天数")
    private Integer totalDays;
    
    @Schema(description = "已释放天数")
    private Integer releasedDays;
    
    @Schema(description = "每日释放金额")
    private BigDecimal dailyReleaseAmount;
    
    @Schema(description = "状态: 0-待激活 1-锁仓中 2-已完成 3-已取消")
    private Integer status;
    
    @Schema(description = "创建时间")
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date updateTime;
    
    public LockOrderVo(LockOrder lockOrder) {
        this.id = lockOrder.getId();
        this.orderNo = lockOrder.getOrderNo();
        this.userId = lockOrder.getUserId();
        this.sourceType = lockOrder.getSourceType();
        this.sourceId = lockOrder.getSourceId();
        this.tokenId = lockOrder.getTokenId();
        this.lockAmount = lockOrder.getLockAmount();
        this.releasedAmount = lockOrder.getReleasedAmount();
        this.availableAmount = lockOrder.getAvailableAmount();
        this.totalDays = lockOrder.getTotalDays();
        this.releasedDays = lockOrder.getReleasedDays();
        this.dailyReleaseAmount = lockOrder.getDailyReleaseAmount();
        this.status = lockOrder.getStatus();
        this.createTime = lockOrder.getCreateTime();
        this.updateTime = lockOrder.getUpdateTime();
    }
}