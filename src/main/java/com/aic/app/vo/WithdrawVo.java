package com.aic.app.vo;

import com.aic.app.model.Withdraw;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class WithdrawVo extends BaseVo {

    /**
     */
    @Schema(description = "提取类型：0-提取锁仓释放, 1-提取质押静态池, 2-提取质押动态池, 3-提取锁仓静态池, 4-提取锁仓动态池")
    private Integer type;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * TOKEN
     */
    @Schema(description = "TOKEN")
    private String tokenId;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String address;

    /**
     * token
     */
    @Schema(description = "token")
    private String token;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private BigDecimal quantity;

    /**
     * 手续费
     */
    @Schema(description = "手续费")
    private BigDecimal fee;

    /**
     * 交易号
     */
    @Schema(description = "交易号")
    private String txid;

    /**
     * 状态 状态 0-待确认 1-审核中 2-成功 3-拒绝回退 4-取消提现
     */
    @Schema(description = "状态 0-待确认 1-审核中 2-成功 3-拒绝回退 4-取消提现")
    private Integer state;

    /**
     * 失败原因
     */
    @Schema(description = "失败原因")
    private String err;

    public WithdrawVo(Withdraw withdraw) {
        BeanUtils.copyProperties(withdraw, this);
    }
}
