package com.aic.app.service;

import com.aic.app.model.LockOrder;
import com.aic.app.model.Product;
import com.aic.app.model.UserProduct;
import com.aic.app.vo.LockOrderVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * 锁仓订单服务接口
 */
public interface ILockOrderService extends IService<LockOrder> {
    
    /**
     * 根据产品类型创建锁仓订单
     */
    LockOrder createLockOrderByProduct(UserProduct userProduct, Product product, BigDecimal amount);
    /**
     * 记录锁仓相关日志
     */
    void recordLockOrderLog(Long userId, Integer logType, BigDecimal amount,
                           String operation, Long lockOrderId, Integer sourceType);

    /**
     * 提取锁仓释放金额
     */
    BigDecimal withdrawLockOrderRelease(Long userId, Long lockOrderId, BigDecimal amount);

    /**
     * 回滚提现失败
     * @param lockOrderId
     * @param amount
     */
    void rollbackWithdrawFailed(Long lockOrderId, BigDecimal amount);
    
    /**
     * 管理员分页查询锁仓订单（包含用户地址）
     */
    IPage<LockOrderVo> pageForAdmin(Page<?> page, Long userId, String orderNo, String address, Integer status, Integer sourceType, String tokenId);
}