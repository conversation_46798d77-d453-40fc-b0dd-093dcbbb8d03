package com.aic.app.service;

import com.aic.app.form.WithdrawNormalProductForm;
import com.aic.app.model.User;
import com.aic.app.model.UserProduct;
import com.aic.app.service.RustService;
import com.aic.app.vo.BondPurchaseRecordVo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

public interface IUserProductService extends IService<UserProduct> {
    
    BigDecimal sum();
    
    UserProduct findUserProduct(String userId, int productId);

    UserProduct getUserProduct(String uid, int id);

    void updateProduct6Rate();

    IPage<UserProduct> pageChildOrders(Page<UserProduct> of, Wrapper<UserProduct> qw);

    /**
     * 分页查询债券购买记录
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 债券购买记录分页数据
     */
    IPage<BondPurchaseRecordVo> pageBondPurchaseRecords(Page<BondPurchaseRecordVo> page, Wrapper<UserProduct> queryWrapper);

    BigDecimal sumAmountByUserIdAndProductType(Long userId, Integer productType);

}
