package com.aic.app.service.impl;

import com.aic.app.exception.Errors;
import com.aic.app.form.StakeForm;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserMapper;
import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.aic.app.util.BizAssert;
import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class StakeUserServiceImpl extends ServiceImpl<StakeUserMapper, StakeUser> implements IStakeUserService {
    @Resource
    IProductService productService;
    @Resource
    IUserProductService userProductService;
    @Resource
    IUserLogService userLogService;
    @Resource
    IUserService userService;
    @Resource
    UserMapper userMapper;
    @Resource
    IUserStakeService userStakeService;
    @Resource
    IWithdrawService withdrawService;
    @Resource
    UserRelationMapper userRelationMapper;
    @Resource
    BizLogService bizLogService;
    @Resource
    ILockOrderService lockOrderService;

    public User initUser(String address) {
        User user = new User();
        user.setType(1);
        user.setCode(Utils.generateInviteCode());
        user.setAddress(address);
        user.setQuadrupleRewardTimes(5); // 设置初始4倍收益次数为5
        userService.add(user);
//        userAssetService.initAsset(user.getId(), this::saveBatch);
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User checkUser(String address) {
        User user = userService.findByAddress(address);
        if (user == null) {
            user = initUser(address);
            checkStakeUser(user.getId(), List.of(AssetEnum.XYC));
        } else {
            final Long userId = user.getId();
            checkStakeUser(userId, List.of(AssetEnum.XYC));
        }
        return user;
    }


    @Transactional(rollbackFor = Exception.class)
    public void checkStakeUser(Long userId, List<AssetEnum> list) {
        List<StakeUser> userStakes = this.list(new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, userId));
        List<StakeUser> newUserStakes = new ArrayList<>();
        for (AssetEnum asset : list) {
            // 如果不存在userStakes中，就创建一个
            StakeUser stakeUser = userStakes.stream().filter(row -> row.getTokenId().equals(asset.getTokenId())).findFirst().orElse(null);
            if (stakeUser == null) {
                stakeUser = new StakeUser();
                stakeUser.setUserId(userId);
                stakeUser.setTokenId(asset.getTokenId());
                newUserStakes.add(stakeUser);
            }
        }
        // 如果有新的，就批量保存
        if (!newUserStakes.isEmpty()) {
            this.saveBatch(newUserStakes);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean withdraw(StakeUser user, String tokenId, BigDecimal amount) {
//        BigDecimal canReceive = user.getCanReceive();
//        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.RECEIVE_EXCEPTION);
//        BizAssert.isTrue(canReceive.compareTo(amount) >= 0, () -> Errors.RECEIVE_EXCEPTION);
//        com.aic.app.model.UserAsset asset = userAssetService.getAsset(user.getUserId(), tokenId);
//        log.info("领取收益 UID = {}, amount = {}", user.getId(), amount);
//        boolean result = update(new UpdateWrapper<StakeUser>()
//                .setSql("can_receive = can_receive - {0}", amount)
//                .apply("can_receive - {0} >= 0", amount)
//                .eq("id", user.getId()));
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
//        return userAssetService.plus(user.getUserId(), asset.getTokenId(), amount, UserLogType.RECEIVE.getValue(), UserLogType.RECEIVE.getLabel(), asset.getTokenId());
        return false;
    }

    @Override
    public IPage<UserModel> findAllForAdminUser(Page<User> page, QueryWrapper<User> queryWrapper) {
        return baseMapper.findAllForAdminUser(page, queryWrapper);
    }

    @Override
    public IPage<StakeUserModel> findAllForAdmin(Page<StakeUser> page, QueryWrapper<StakeUser> queryWrapper) {
        return baseMapper.findAllForAdmin(page, queryWrapper);
    }

    @Override
    public StakeUser getStakeUser(Long userId, String tokenId) {
        return this.getOne(new LambdaQueryWrapper<StakeUser>()
                .eq(StakeUser::getUserId, userId)
                .eq(StakeUser::getTokenId, tokenId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stake(User user, StakeForm form) {
//        BigDecimal amount = form.getAmount();
//        String token = form.getTokenId();
//
//        BizAssert.notNull(user.getPid(), () -> Errors.NO_INVITE_EXCEPTION);
//
//        log.info("质押 uid = {}, token = {}, amount = {}", user.getId(), token, amount);
//        com.aic.app.model.UserAsset asset = userAssetService.getAsset(user.getId(), token);
//        BizAssert.notNull(asset, () -> Errors.BALANCE_EXCEPTION);
//        BizAssert.isTrue(asset.checkBalance(amount), () -> Errors.BALANCE_EXCEPTION);
//
//        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
//
//        bizLogService.recordLog(user.getId(), null, "质押", form, "质押【" + token + "】");
//
//        // 扣款
//        userAssetService.pay(user.getId(), asset.getTokenId(), asset.getTokenId(), amount, UserLogType.Stake.getValue(), UserLogType.Stake.getLabel() + "【" + token + "】");
//
//        int updateRow = baseMapper.updateUserPendingAmount(stakeUser.getId(), amount);
//        BizAssert.isTrue(updateRow == 1, () -> Errors.LIMIT_AMOUNT_EXCEPTION);
//
//        if (!Boolean.TRUE.equals(stakeUser.getStakeFirst())) {
//            // 首次质押增加4倍额度
//            BigDecimal limit = amount.multiply(BigDecimal.valueOf(4));
//            updateRow = baseMapper.updateUserLimitAmount(stakeUser.getId(), limit);
//            log.info("[stake] 首次质押，增加4倍额度，UID = {}, 额度 = {}", stakeUser.getId(), limit);
//            BizAssert.isTrue(updateRow == 1, () -> Errors.LIMIT_AMOUNT_EXCEPTION);
//        }
//
//        UserStake userStake = new UserStake(0, user.getId(), asset.getTokenId(), amount);
//        userStakeService.save(userStake);
//
//        // this.updateTeamPerf(user.getId(), asset.getTokenId(), amount);
//
//        assetService.updateTotal(asset.getTokenId(), amount, null, null, null, null, null, null);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stakeFromWallet(Events events) {
        Events.Data data = events.getData();

        // 验证必要字段
        if (data.getAddress() == null || data.getStakeAmount() == null) {
            log.error("[stakeFromWallet] 事件数据不完整，address = {}, stakeAmount = {}",
                    data.getAddress(), data.getStakeAmount());
            return;
        }

        // 根据地址查找或创建用户
        User user = checkUser(data.getAddress());
        if (user == null) {
            log.error("[stakeFromWallet] 用户创建失败，address = {}", data.getAddress());
            return;
        }

        String tokenId = AssetEnum.XYC.getTokenId();
        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);

        try {
            BigDecimal amount = new BigDecimal(data.getStakeAmount());

            // 记录业务日志
            bizLogService.recordLog(user.getId(), null, "质押", data,
                    "质押【" + tokenId + "】金额: " + amount + ", 交易号: " + events.getTxid());

            log.info("[stakeFromWallet] 处理质押事件，userId = {}, tokenId = {}, amount = {}, txid = {}",
                    user.getId(), tokenId, amount, events.getTxid());

            // 更新用户待确认质押金额
            int updateRow = baseMapper.updateUserPendingAmount(stakeUser.getId(), amount);
            if (updateRow != 1) {
                log.error("[stakeFromWallet] 更新待确认质押金额失败，stakeUserId = {}, amount = {}", stakeUser.getId(), amount);
                BizAssert.isTrue(false, () -> Errors.LIMIT_AMOUNT_EXCEPTION);
            }

            // 记录用户日志
            userLogService.addLog(user.getId(), UserLogType.Stake.getValue(), amount,
                    UserLogType.Stake.getLabel() + "【" + tokenId + "】", tokenId, tokenId);

            // 创建质押记录
            UserStake userStake = new UserStake(0, user.getId(), tokenId, amount);
            // data.getTokenType() 0：ju，1：usdt，2：xyc
            if (data.getTokenType() == 0) {
                userStake.setPayToken(AssetEnum.JU.getTokenId());
            } else if (data.getTokenType() == 1) {
                userStake.setPayToken(AssetEnum.USDT.getTokenId());
            } else if (data.getTokenType() == 2) {
                userStake.setPayToken(AssetEnum.XYC.getTokenId());
            }
            userStake.setOriAmount(new BigDecimal(data.getOriAmount()));
            userStake.setStatus(0); // 待确认状态，等待T+1结算
            userStake.setTxid(events.getTxid());
            userStakeService.save(userStake);

            log.info("[stakeFromWallet] 质押事件处理成功，userId = {}, amount = {}, txid = {}",
                    user.getId(), amount, events.getTxid());

        } catch (NumberFormatException e) {
            log.error("[stakeFromWallet] 质押金额格式错误，stakeAmount = {}", data.getStakeAmount(), e);
        } catch (Exception e) {
            log.error("[stakeFromWallet] 质押事件处理失败，userId = {}, txid = {}",
                    user.getId(), events.getTxid(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSuccess(RustService.CheckResult checkResult) {
        Withdraw withdraw = withdrawService.getById(checkResult.getId());
        if (!withdraw.getState().equals(0)) {
            log.warn("[withdraw] 提现状态更被更新 id = {}, state = {}", withdraw.getId(), withdraw.getState());
            return;
        }
        boolean success = withdrawService.update(new LambdaUpdateWrapper<Withdraw>()
                .set(Withdraw::getState, 1)
                .set(Withdraw::getTxid, checkResult.getTxid())
                .eq(Withdraw::getId, withdraw.getId())
                .eq(Withdraw::getState, 0));
        BizAssert.isTrue(success, () -> Errors.SERVER_EXCEPTION);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFails(RustService.CheckResult checkResult) {
        Withdraw withdraw = withdrawService.getById(checkResult.getId());
        if (!withdraw.getState().equals(0) && !withdraw.getState().equals(1)) {
            log.warn("[withdraw] 提现状态更被更新 id = {}, state = {}", withdraw.getId(), withdraw.getState());
            return;
        }
        boolean success = withdrawService.update(new LambdaUpdateWrapper<Withdraw>()
                .set(Withdraw::getState, 4)
                .eq(Withdraw::getId, withdraw.getId())
                .eq(Withdraw::getState, withdraw.getState()));
        BizAssert.isTrue(success, () -> Errors.SERVER_EXCEPTION);

        Long userId = withdraw.getUserId();
        String tokenId = withdraw.getTokenId();
        BigDecimal amount = withdraw.getQuantity();

        StakeUser stakeUser = this.getStakeUser(userId, tokenId);

        if (withdraw.getType().equals(0)) {
            // 返回提取锁仓释放金额
            log.info("[withdraw] 超时 返回提取锁仓释放金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            lockOrderService.rollbackWithdrawFailed(withdraw.getLockOrderId(), amount);
            userLogService.save(new UserLog(userId, UserLogType.RECEIVE.getValue(), BigDecimal.ZERO, amount, tokenId, tokenId, "提取锁仓释放【资金退回】"));
        } else if (withdraw.getType().equals(1)) {
            // 返回提取质押静态池金额
            log.info("[withdraw] 超时 返回提取质押静态池金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            int updateRow = baseMapper.updateStaticPool(stakeUser.getId(), amount.negate());
            BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
            userLogService.save(new UserLog(userId, UserLogType.RECEIVE.getValue(), BigDecimal.ZERO, amount, tokenId, tokenId, "提取质押静态池【资金退回】"));
        } else if (withdraw.getType().equals(2)) {
            // 返回提取质押动态池金额
            log.info("[withdraw] 超时 返回提取质押动态池金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            int updateRow = baseMapper.updateDynamicPool(stakeUser.getId(), amount.negate());
            BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
            userLogService.save(new UserLog(userId, UserLogType.RECEIVE.getValue(), BigDecimal.ZERO, amount, tokenId, tokenId, "提取质押动态池【资金退回】"));
        } else if (withdraw.getType().equals(3)) {
            // 返回提取锁仓静态池金额
            log.info("[withdraw] 超时 返回提取锁仓静态池金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            int updateRow = baseMapper.updateLockStaticPool(stakeUser.getId(), amount.negate());
            BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
            userLogService.save(new UserLog(userId, UserLogType.RECEIVE.getValue(), BigDecimal.ZERO, amount, tokenId, tokenId, "提取锁仓静态池【资金退回】"));
        } else if (withdraw.getType().equals(4)) {
            // 返回提取锁仓动态池金额
            log.info("[withdraw] 超时 返回提取锁仓动态池金额 UID = {}, amount = {}", stakeUser.getId(), amount);
            int updateRow = baseMapper.updateLockDynamicPool(stakeUser.getId(), amount.negate());
            BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
            userLogService.save(new UserLog(userId, UserLogType.RECEIVE.getValue(), BigDecimal.ZERO, amount, tokenId, tokenId, "提取锁仓动态池【资金退回】"));
        }
    }


    @Override
    public StakeUser sumChilds(Long id) {
        UserRelation userRelation = userRelationMapper.selectById(id);
        if (userRelation == null) {
            return new StakeUser()
                    .setTotalStatic(BigDecimal.ZERO)
                    .setTotalDynamic(BigDecimal.ZERO);
        }
        String path = Optional.ofNullable(userRelation.getPath()).orElse("/" + id);
        return getBaseMapper().sumChilds(path + "/%");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawStakeStaticPool(Long userId, String tokenId, BigDecimal amount) {
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.WITHDRAW_AMOUNT_INVALID);

        StakeUser stakeUser = getStakeUser(userId, tokenId);
        BizAssert.notNull(stakeUser, () -> Errors.WITHDRAW_USER_RECORD_NOT_FOUND);

        BigDecimal staticPool = stakeUser.getStaticPool() != null ? stakeUser.getStaticPool() : BigDecimal.ZERO;
        BizAssert.isTrue(staticPool.compareTo(amount) >= 0, () -> Errors.WITHDRAW_STATIC_POOL_INSUFFICIENT);

        // 扣减静态池金额
        int updateRow = baseMapper.updateStaticPool(stakeUser.getId(), amount);
        BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_OPERATION_FAILED);

        // 记录用户日志
        userLogService.addLog(userId, UserLogType.RECEIVE.getValue(), amount,
            "提取质押静态池", tokenId, tokenId);

        log.info("用户 {} 提取质押静态池成功，金额：{}", userId, amount);
        return amount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawStakeDynamicPool(Long userId, String tokenId, BigDecimal amount) {
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.WITHDRAW_AMOUNT_INVALID);

        StakeUser stakeUser = getStakeUser(userId, tokenId);
        BizAssert.notNull(stakeUser, () -> Errors.WITHDRAW_USER_RECORD_NOT_FOUND);

        BigDecimal dynamicPool = stakeUser.getDynamicPool() != null ? stakeUser.getDynamicPool() : BigDecimal.ZERO;
        BizAssert.isTrue(dynamicPool.compareTo(amount) >= 0, () -> Errors.WITHDRAW_DYNAMIC_POOL_INSUFFICIENT);

        // 扣减动态池金额
        int updateRow = baseMapper.updateDynamicPool(stakeUser.getId(), amount);
        BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_OPERATION_FAILED);

        // 记录用户日志
        userLogService.addLog(userId, UserLogType.RECEIVE.getValue(), amount,
            "提取质押动态池", tokenId, tokenId);

        log.info("用户 {} 提取质押动态池成功，金额：{}", userId, amount);
        return amount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawLockStaticPool(Long userId, String tokenId, BigDecimal amount) {
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.WITHDRAW_AMOUNT_INVALID);

        StakeUser stakeUser = getStakeUser(userId, tokenId);
        BizAssert.notNull(stakeUser, () -> Errors.WITHDRAW_USER_RECORD_NOT_FOUND);

        BigDecimal lockStaticPool = stakeUser.getLockStaticPool() != null ? stakeUser.getLockStaticPool() : BigDecimal.ZERO;
        BizAssert.isTrue(lockStaticPool.compareTo(amount) >= 0, () -> Errors.WITHDRAW_LOCK_STATIC_POOL_INSUFFICIENT);

        // 扣减锁仓静态池金额
        int updateRow = baseMapper.updateLockStaticPool(stakeUser.getId(), amount);
        BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_OPERATION_FAILED);

        // 记录用户日志
        userLogService.addLog(userId, UserLogType.RECEIVE.getValue(), amount,
            "提取锁仓静态池", tokenId, tokenId);

        log.info("用户 {} 提取锁仓静态池成功，金额：{}", userId, amount);
        return amount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawLockDynamicPool(Long userId, String tokenId, BigDecimal amount) {
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.WITHDRAW_AMOUNT_INVALID);

        StakeUser stakeUser = getStakeUser(userId, tokenId);
        BizAssert.notNull(stakeUser, () -> Errors.WITHDRAW_USER_RECORD_NOT_FOUND);

        BigDecimal lockDynamicPool = stakeUser.getLockDynamicPool() != null ? stakeUser.getLockDynamicPool() : BigDecimal.ZERO;
        BizAssert.isTrue(lockDynamicPool.compareTo(amount) >= 0, () -> Errors.WITHDRAW_LOCK_DYNAMIC_POOL_INSUFFICIENT);

        // 扣减锁仓动态池金额
        int updateRow = baseMapper.updateLockDynamicPool(stakeUser.getId(), amount);
        BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_OPERATION_FAILED);

        // 记录用户日志
        userLogService.addLog(userId, UserLogType.RECEIVE.getValue(), amount,
            "提取锁仓动态池", tokenId, tokenId);

        log.info("用户 {} 提取锁仓动态池成功，金额：{}", userId, amount);
        return amount;
    }
}
