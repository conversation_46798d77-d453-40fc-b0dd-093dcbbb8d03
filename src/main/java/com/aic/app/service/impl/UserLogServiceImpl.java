package com.aic.app.service.impl;

import com.aic.app.mapper.UserLogMapper;
import com.aic.app.model.*;
import com.aic.app.service.IUserLogService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.hutool.core.date.DateUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class UserLogServiceImpl extends ServiceImpl<UserLogMapper, UserLog> implements IUserLogService {

    public UserLog addLog(Long userId, int userLogType, BigDecimal amount, String... remark) {
        UserLog userLog = new UserLog(userId, userLogType, BigDecimal.ZERO, amount, remark.length > 0 ? remark[0] : null);
        userLog.setSymbol(remark.length > 1 ? remark[1] : "");
        userLog.setTokenId(remark.length > 2 ? remark[2] : null);
        this.save(userLog);
        return userLog;
    }

    @Override
    public <E extends IPage<UserLog>> E page(E page, Wrapper<UserLog> queryWrapper) {
        return getBaseMapper().page(page, queryWrapper);
    }

    @Override
    public BigDecimal getTodayAmount() {
        return getBaseMapper().getTodayAmount(DateUtil.beginOfDay(DateUtil.date()));
    }

}
