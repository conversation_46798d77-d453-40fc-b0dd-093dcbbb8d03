package com.aic.app.service.impl;

import com.aic.app.mapper.EventsMapper;
import com.aic.app.mapper.WithdrawMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
@Slf4j
public class EventsServiceImpl extends ServiceImpl<EventsMapper, Events>
    implements IEventsService {

    @Resource
    IProductService productService;
    @Resource
    IStakeUserService stakeUserService;
    @Resource
    IUserService userService;
    @Resource
    IUserProductService userProductService;
    @Resource
    IUserStakeService userStakeService;
    @Resource
    BizLogService bizLogService;
    @Resource
    IUserLogService userLogService;
    @Resource
    ILockOrderService lockOrderService;
    @Resource
    IProductPurchaseService productPurchaseService;
    @Resource
    WithdrawMapper withdrawMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleEvent(Events events) {
        if (!events.getStatus().equals(0)) {
            log.warn("[event] 事件已处理 {}", events);
            return;
        }

        log.info("[event] 处理事件，类型 = {}, 事件ID = {}",
                events.getName(), events.getId());

        switch (events.getName()) {
            case "BuyInfo":
                handleBuyInfoEvent(events);
                break;
            case "ReferralBound":
                handleReferralBoundEvent(events);
                break;
            case "Stake":
                handleStakeEvent(events);
                break;
            case "UnStake":
                handleUnStakeEvent(events);
                break;
            case "WithdrawToken":
                handleWithdrawTokenEvent(events);
                break;
            default:
                log.warn("[event] 未处理事件 {}", events.getName());
                return;
        }

        this.update(new LambdaUpdateWrapper<Events>()
                .set(Events::getStatus, 1)
                .eq(BaseEntity::getId, events.getId()));

    }

    private void handleWithdrawTokenEvent(Events events) {
        Events.Data data = events.getData();
        log.info("[event] 处理WithdrawToken事件，data = {}", data);

        // 验证必要字段
        if (data.getNonce() == null || data.getTxid() == null) {
            log.error("[event] WithdrawToken事件数据不完整，nonce = {}, txid = {}",
                    data.getNonce(), data.getTxid());
            return;
        }

        Long nonce = data.getNonce();
        String txid = data.getTxid();

        // 根据nonce查询对应的提现记录
        Withdraw withdraw = withdrawMapper.selectOne(
                new LambdaQueryWrapper<Withdraw>()
                        .eq(Withdraw::getNonce, nonce)
                        .last("limit 1")
        );

        if (withdraw == null) {
            log.warn("[event] 找不到对应的提现记录，nonce = {}", nonce);
            return;
        }

        log.info("[event] 找到提现记录，id = {}, userId = {}, state = {}",
                withdraw.getId(), withdraw.getUserId(), withdraw.getState());

        // 检查记录状态是否为待确认（0）
        if (withdraw.getState() != 0) {
            log.warn("[event] 提现记录状态不是待确认，状态 = {}, id = {}", withdraw.getState(), withdraw.getId());
            return;
        }

        // 使用乐观锁更新记录状态为成功（2），并保存txid
        boolean updated = withdrawMapper.update(
                null,
                new LambdaUpdateWrapper<Withdraw>()
                        .set(Withdraw::getState, 2)
                        .set(Withdraw::getTxid, txid)
                        .eq(Withdraw::getId, withdraw.getId())
                        .eq(Withdraw::getState, 0) // 乐观锁条件
        ) > 0;

        if (updated) {
            log.info("[event] 提现记录更新成功，id = {}, txid = {}", withdraw.getId(), txid);
        } else {
            log.warn("[event] 提现记录更新失败，可能已被其他线程处理，id = {}", withdraw.getId());
        }
    }

    /**
     * 处理BuyInfo事件
     * @param events 事件对象
     */
    private void handleBuyInfoEvent(Events events) {
        Events.Data data = events.getData();
        log.info("[event] 处理BuyInfo事件，data = {}", data);
        
        // 验证必要字段
        if (data.getAddress() == null || data.getPid() == null || data.getValue() == null || data.getAmount() == null) {
            log.error("[event] BuyInfo事件数据不完整，address = {}, pid = {}, value = {}, amount = {}", 
                    data.getAddress(), data.getPid(), data.getValue(), data.getAmount());
            return;
        }
        
        // 根据address查询用户，如果不存在则自动创建
        User user = stakeUserService.checkUser(data.getAddress());
        
        // 根据pid查询product
        Product product = productService.getById(data.getPid());
        if (product == null) {
            log.warn("[event] 找不到产品，pid = {}", data.getPid());
            return;
        }
        
        // 验证价格和金额
        try {
            BigDecimal eventValue = new BigDecimal(data.getValue());
            BigDecimal eventAmount = new BigDecimal(data.getAmount());
            BigDecimal productPrice = product.getPrice();
            BigDecimal eventPrice = new BigDecimal(data.getPrice());
            
            // 比较实际金额与期望金额（允许小的精度误差）
            if (productPrice.compareTo(eventPrice) != 0) {
                log.error("[event] 价格验证失败，产品价格 = {}, 数量 = {}, 实际价格 = {}, 实际金额 = {}",
                        productPrice, eventAmount, eventPrice, eventValue);
                return;
            }
            
            log.info("[event] 价格验证成功，产品价格 = {}, 数量 = {}, 金额 = {}", 
                    productPrice, eventAmount, eventValue);
            
        } catch (NumberFormatException e) {
            log.error("[event] 数值格式错误，value = {}, amount = {}", data.getValue(), data.getAmount(), e);
            return;
        }
        
        // 根据产品类型处理不同的逻辑
        if (product.getType() == 1) {
            // Type 1: 预售节点 - 更新等级并插入订单
            handleType1Product(user, product, data);
        } else if (product.getType() == 2) {
            // Type 2: 普通商品 - 只插入订单，不更新等级
            handleType2Product(user, product, data);
        } else if (product.getType() == 3) {
            // Type 3: 债券产品 - 创建锁仓订单
            handleType3Product(user, product, data);
        } else {
            log.warn("[event] 未知的产品类型，productId = {}, type = {}", product.getId(), product.getType());
        }
     }

     /**
      * 处理ReferralBound事件
      * @param events 事件对象
      */
     private void handleReferralBoundEvent(Events events) {
         Events.Data data = events.getData();
         log.info("[event] 处理ReferralBound事件，data = {}", data);

         // 验证必要字段
         if (data.getUser() == null || data.getReferrer() == null) {
             log.error("[event] ReferralBound事件数据不完整，user = {}, referrer = {}",
                     data.getUser(), data.getReferrer());
             return;
         }

         // 检查用户是否存在，如果不存在则自动创建
         User user = stakeUserService.checkUser(data.getUser());
         log.info("[event] 检查用户，userId = {}, address = {}", user.getId(), user.getAddress());

         // 检查推荐人是否存在，如果不存在则自动创建
         User referrer = stakeUserService.checkUser(data.getReferrer());
         log.info("[event] 检查推荐人，referrerId = {}, address = {}", referrer.getId(), referrer.getAddress());

         // 检查用户是否已有邀请人
         if (user.hasParent()) {
             log.info("[event] 用户已有邀请人，忽略绑定，userId = {}, currentPid = {}", user.getId(), user.getPid());
             return;
         }

         // 检查不能自己邀请自己
         if (user.getId().equals(referrer.getId())) {
             log.warn("[event] 用户不能邀请自己，userId = {}, referrerId = {}", user.getId(), referrer.getId());
             return;
         }

         // 绑定邀请人
         boolean success = userService.update(new LambdaUpdateWrapper<User>()
                 .set(User::getPid, referrer.getId())
                 .eq(User::getId, user.getId())
                 .isNull(User::getPid)); // 确保只有没有邀请人的用户才能绑定

         if (success) {
             log.info("[event] 成功绑定邀请人，userId = {}, referrerId = {}", user.getId(), referrer.getId());
         } else {
             log.warn("[event] 绑定邀请人失败，可能用户已有邀请人，userId = {}, referrerId = {}", user.getId(), referrer.getId());
         }
     }

     /**
      * 处理Type 1产品（预售节点）- 更新等级并插入订单
      * @param user 用户
      * @param product 产品
      * @param data 事件数据
      */
     public void handleType1Product(User user, Product product, Events.Data data) {

         BigDecimal quantity = new BigDecimal(data.getAmount().toString());
         BigDecimal price = new BigDecimal(data.getPrice().toString());
         BigDecimal amount = quantity.multiply(price);
         productPurchaseService.purchaseProduct(user, product, amount, quantity);

         // 获取用户类型1订单的累计金额
         BigDecimal totalAmount = userProductService.sumAmountByUserIdAndProductType(user.getId(), 1);
         log.info("[event] 用户 {} 类型1订单累计金额: {}", user.getId(), totalAmount);

         // 根据累计金额更新用户等级
         int newLevel = user.getPreLevel(); // 默认保持当前等级
         if (totalAmount.compareTo(new BigDecimal("50000")) >= 0) {
             newLevel = 6;
         } else if (totalAmount.compareTo(new BigDecimal("20000")) >= 0) {
             newLevel = 5;
         } else if (totalAmount.compareTo(new BigDecimal("5000")) >= 0) {
             newLevel = 4;
         } else if (totalAmount.compareTo(new BigDecimal("1000")) >= 0) {
             newLevel = 3;
         } else if (totalAmount.compareTo(new BigDecimal("100")) >= 0) {
             newLevel = 2;
         }

         // 更新用户的preLevel字段为product的day字段
         if (newLevel > user.getPreLevel()) {
             user.setPreLevel(newLevel);
             if (user.getPreLevel() > user.getLevel()) {
                 user.setLevel(user.getPreLevel());
             }
             userService.update(new LambdaUpdateWrapper<User>()
                     .set(User::getLevel, user.getLevel())
                     .set(User::getPreLevel, user.getPreLevel())
                     .eq(User::getId, user.getId()));
             log.info("[event] Type1产品 - 更新用户预售等级，userId = {}, preLevel = {}, level = {}", user.getId(), user.getPreLevel(), user.getLevel());
         } else {
             log.info("[event] Type1产品 - 不更新用户预售等级，userId = {}, preLevel = {}, level = {}", user.getId(), user.getPreLevel(), user.getLevel());
         }

     }

     /**
      * 处理Type 2产品（普通商品）- 只插入订单，不更新等级
      * @param user 用户
      * @param product 产品
      * @param data 事件数据
      */
     private void handleType2Product(User user, Product product, Events.Data data) {
         log.info("[event] Type2产品 - 只创建订单，不更新等级，userId = {}, productId = {}", user.getId(), product.getId());

         // 创建UserProduct记录
//         createUserProduct(user, product, data, "Type2普通商品");
         BigDecimal quantity = new BigDecimal(data.getAmount().toString());
         BigDecimal price = new BigDecimal(data.getPrice().toString());
         BigDecimal amount = quantity.multiply(price);
         productPurchaseService.purchaseProduct(user, product, amount, quantity);
     }

     /**
      * 处理Type 3产品（债券产品）- 创建锁仓订单
      * @param user 用户
      * @param product 产品
      * @param data 事件数据
      */
     private void handleType3Product(User user, Product product, Events.Data data) {
         log.info("[event] Type3产品 - 创建债券锁仓订单，userId = {}, productId = {}, 锁仓天数 = {}",
                 user.getId(), product.getId(), product.getDay());

         try {
             BigDecimal value = new BigDecimal(data.getValue());
             BigDecimal price = new BigDecimal(data.getPrice());
            //  BigDecimal quantity = value.divide(price, 8, RoundingMode.HALF_UP);
             // 债券售出的单价是U，锁仓的单位是XYC，需要将uvalue转成xyc
             BigDecimal xycPrice = productService.getPrice(AssetEnum.XYC.getTokenId());
             BigDecimal amount = value.divide(xycPrice, 8, RoundingMode.HALF_UP);

             // 先创建UserProduct记录
             UserProduct userProduct = new UserProduct(user.getId(), Utils.genOrderNo(), product, BigDecimal.ZERO);
             userProduct.setAmount(value);
             userProduct.setPrice(price);
             userProduct.setQuantity(value);
             userProduct.setType(user.getType());
             userProductService.save(userProduct);

             productPurchaseService.afterProductPurchase(userProduct, product);

             // 创建对应的锁仓订单，根据产品的天数设置锁仓期限
             LockOrder lockOrder = lockOrderService.createLockOrderByProduct(userProduct, product, amount);

             log.info("[event] Type3债券产品 - 创建锁仓订单成功，userId = {}, productId = {}, 锁仓订单号 = {}, 锁仓天数 = {}, uValue = {}, xycPrice = {}, 锁仓金额 = {}",
                     user.getId(), product.getId(), lockOrder.getOrderNo(), lockOrder.getTotalDays(), value, xycPrice, lockOrder.getLockAmount());

         } catch (NumberFormatException e) {
             log.error("[event] Type3债券产品 - 金额格式错误，amount = {}, price = {}", data.getAmount(), data.getPrice(), e);
         } catch (Exception e) {
             log.error("[event] Type3债券产品 - 创建锁仓订单失败，userId = {}, productId = {}", user.getId(), product.getId(), e);
         }
     }

     /**
      * 创建用户产品记录
      * @param user 用户
      * @param product 产品
      * @param data 事件数据
      * @param productTypeDesc 产品类型描述
      */
     private void createUserProduct(User user, Product product, Events.Data data, String productTypeDesc) {
         try {
             BigDecimal quantity = new BigDecimal(data.getAmount().toString());
             BigDecimal price = new BigDecimal(data.getPrice().toString());
             BigDecimal amount = quantity.multiply(price);
             UserProduct userProduct = new UserProduct(user.getId(), Utils.genOrderNo(), product, BigDecimal.ZERO);
             userProduct.setAmount(amount);
             userProduct.setPrice(price);
             userProduct.setQuantity(quantity);
             // userProduct.setTokenId("BNB"); // 根据实际情况设置tokenId
             userProduct.setType(user.getType());

             // 如果是普通商品(type=2)，初始化线性释放相关字段
             if (product.getType() == 1 || product.getType() == 2) {
                 // 计算每日释放金额 = 总金额 / 360天
                 BigDecimal dailyRelease = amount.divide(new BigDecimal("360"), 8, BigDecimal.ROUND_HALF_UP);
                 userProduct.setDailyReleaseAmount(dailyRelease);
                 userProduct.setTotalReleased(BigDecimal.ZERO);
                 userProduct.setAvailableAmount(BigDecimal.ZERO);
                 userProduct.setLastReleaseDate(null);

                 log.info("[event] 普通商品订单初始化线性释放，订单号={}, 总金额={}, 每日释放={}",
                     userProduct.getOrderNo(), amount, dailyRelease);
             }

             userProductService.save(userProduct);
             
             log.info("[event] {} - 创建用户产品记录，userId = {}, productId = {}, amount = {}", 
                     productTypeDesc, user.getId(), product.getId(), amount);
         } catch (NumberFormatException e) {
             log.error("[event] {} - 金额格式错误，amount = {}", productTypeDesc, data.getAmount(), e);
         }
     }

     /**
      * 处理Stake事件
      * @param events 事件对象
      */
     private void handleStakeEvent(Events events) {
         log.info("[event] 处理Stake事件，事件ID = {}", events.getId());

         // 调用质押处理方法
         stakeUserService.stakeFromWallet(events);
     }

    /**
     * 处理UnStake事件
     * @param events 事件对象
     */
    private void handleUnStakeEvent(Events events) {
        Events.Data data = events.getData();
        log.info("[event] 处理UnStake事件，data = {}", data);

        // 验证必要字段
        if (data.getAddress() == null || data.getStakeAmount() == null) {
            log.error("[event] UnStake事件数据不完整，address = {}, stakeAmount = {}",
                    data.getAddress(), data.getStakeAmount());
            return;
        }

        // 根据address查询用户，如果不存在则自动创建
        User user = stakeUserService.checkUser(data.getAddress());

        try {
            // 解析解质押金额
            BigDecimal amount = new BigDecimal(data.getStakeAmount());
            String tokenId = "XYC";

            // 获取用户的质押信息
            StakeUser stakeUser = stakeUserService.getStakeUser(user.getId(), tokenId);
            if (stakeUser == null || stakeUser.getId() == null) {
                log.error("[event] UnStake事件找不到用户质押信息，userId = {}, tokenId = {}", user.getId(), tokenId);
                return;
            }

            // 计算扣除逻辑：优先扣除待确认数量，不足时扣除已确认数量
            BigDecimal totalStake = stakeUser.getPendingAmount().add(stakeUser.getCurrentAmount());
            if (totalStake.compareTo(amount) < 0) {
                log.error("[event] UnStake事件质押数量不足，userId = {}, 需要扣除 = {}, 总质押 = {}",
                        user.getId(), amount, totalStake);
                return;
            }

            BigDecimal currentAmount = BigDecimal.ZERO;
            BigDecimal pendingAmount = BigDecimal.ZERO;

            if (stakeUser.getPendingAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (stakeUser.getPendingAmount().compareTo(amount) >= 0) {
                    pendingAmount = amount;
                } else {
                    pendingAmount = stakeUser.getPendingAmount();
                    currentAmount = amount.subtract(pendingAmount);
                }
            } else {
                currentAmount = amount;
            }

            // 记录业务日志
            bizLogService.recordLog(user.getId(), null, "赎回质押", data, "赎回质押【" + tokenId + "】");

            log.info("[event] 赎回质押 资产 = {}, UID = {}, amount = {}, 扣减待确认 = {}, 扣减已确认 = {}",
                    tokenId, user.getId(), amount, pendingAmount, currentAmount);

            // 扣除用户质押数量
            boolean result = stakeUserService.update(new UpdateWrapper<StakeUser>()
                    .setSql("current_amount = current_amount - {0}, pending_amount = pending_amount - {1}", currentAmount, pendingAmount)
                    .apply("current_amount - {0} >= 0 and pending_amount - {1} >= 0", currentAmount, pendingAmount)
                    .eq("id", stakeUser.getId()));

            if (!result) {
                log.error("[event] UnStake事件扣除质押数量失败，userId = {}, stakeUserId = {}", user.getId(), stakeUser.getId());
                return;
            }

            // 记录用户日志
            userLogService.addLog(user.getId(), UserLogType.RedeemCurrent.getValue(), amount, "赎回质押【" + tokenId + "】", tokenId, tokenId);

            // 创建UserStake记录 - type=1表示解除质押
            UserStake userStake = new UserStake(1, user.getId(), tokenId, amount);
            userStake.setTxid(data.getTxid());
            userStake.setStatus(1); // 待确认状态

            // 保存解质押记录
            userStakeService.save(userStake);

            log.info("[event] UnStake事件处理成功，userId = {}, stakeAmount = {}, txid = {}, 扣减待确认 = {}, 扣减已确认 = {}",
                    user.getId(), amount, data.getTxid(), pendingAmount, currentAmount);

        } catch (NumberFormatException e) {
            log.error("[event] UnStake事件数值格式错误，stakeAmount = {}",
                    data.getStakeAmount(), e);
        }
    }
}




