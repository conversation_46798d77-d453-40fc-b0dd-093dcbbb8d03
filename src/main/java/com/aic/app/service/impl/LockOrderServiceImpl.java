package com.aic.app.service.impl;

import com.aic.app.mapper.LockOrderMapper;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.model.*;
import com.aic.app.service.BizLogService;
import com.aic.app.service.ILockOrderService;
import com.aic.app.service.IUserLogService;
import com.aic.app.service.ISysConfigService;
import com.aic.app.util.BizAssert;
import com.aic.app.exception.Errors;
import com.aic.app.vo.LockOrderVo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 锁仓订单服务实现
 */
@Service
@Slf4j
public class LockOrderServiceImpl extends ServiceImpl<LockOrderMapper, LockOrder> implements ILockOrderService {
    
    @Resource
    private IUserLogService userLogService;
    
    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private BizLogService bizLogService;
    
    @Resource
    private StakeUserMapper stakeUserMapper;
    
    @Override
    public LockOrder createLockOrderByProduct(UserProduct userProduct, Product product, BigDecimal amount) {
        // 从 UserProduct 中获取 USD 金额（amount 字段存储的是 USD 金额）
        BigDecimal usdAmount = userProduct.getAmount();
        
        LockOrder lockOrder = new LockOrder(
            userProduct.getOrderNo(), // 使用UserProduct的订单号，保持一致
            userProduct.getUserId(),
            product.getType(),
            userProduct.getId(),
            AssetEnum.XYC.getTokenId(),
            amount,
            usdAmount
        );
        
        // 根据产品类型设置锁仓规则
        switch (product.getType()) {
            case 1: // 节点产品
                // 节点产品按XYC=10U转换一下锁仓数量，购买的单位是U，转成XYC需要除以10U
                lockOrder.setLockAmount(amount.divide(new BigDecimal("10"), 8, RoundingMode.HALF_UP));
                setupNodeLockOrder(lockOrder, product);
                break;
            case 2: // 普通商品
                setupNormalProductLockOrder(lockOrder, product);
                break;
            case 3: // 债券产品
                setupBondLockOrder(lockOrder, product);
                break;
            default:
                throw new IllegalArgumentException("不支持的产品类型: " + product.getType());
        }
        
        // 保存锁仓订单
        this.save(lockOrder);
        
        // 记录创建日志
        recordLockOrderLog(lockOrder.getUserId(), UserLogType.LOCK_ORDER_CREATE.getValue(),
            lockOrder.getLockAmount(), "创建锁仓订单", lockOrder.getId(), lockOrder.getSourceType());
        
        log.info("创建锁仓订单成功，订单号: {}, 用户ID: {}, 金额: {}", 
            lockOrder.getOrderNo(), lockOrder.getUserId(), lockOrder.getLockAmount());
        
        return lockOrder;
    }
    
    private void setupNodeLockOrder(LockOrder lockOrder, Product product) {
        lockOrder.setTotalDays(360);
        lockOrder.setDailyReleaseAmount(
            lockOrder.getLockAmount().divide(new BigDecimal(lockOrder.getTotalDays()), 8, RoundingMode.HALF_UP)
        );
    }
    
    private void setupNormalProductLockOrder(LockOrder lockOrder, Product product) {
        lockOrder.setTotalDays(360); // 固定360天
        lockOrder.setDailyReleaseAmount(
            lockOrder.getLockAmount().divide(new BigDecimal(lockOrder.getTotalDays()), 8, RoundingMode.HALF_UP)
        );
    }
    
    private void setupBondLockOrder(LockOrder lockOrder, Product product) {
        lockOrder.setDailyReleaseAmount(
            lockOrder.getLockAmount().divide(new BigDecimal(product.getDay()), 8, RoundingMode.HALF_UP)
        );

        lockOrder.setTotalDays(product.getDay());
    }
    
    @Override
    public void recordLockOrderLog(Long userId, Integer logType, BigDecimal amount, 
                                  String operation, Long lockOrderId, Integer sourceType) {
        String remark = String.format("%s - 锁仓订单ID: %d, 产品类型: %d", 
                                     operation, lockOrderId, sourceType);
        bizLogService.recordLog(userId, lockOrderId, operation, amount, remark);
        // 释放记录 userLog
        userLogService.addLog(userId, UserLogType.LOCK_RELEASE_LINEAR.getValue(), amount, remark);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal withdrawLockOrderRelease(Long userId, Long lockOrderId, BigDecimal amount) {
        // 1. 验证锁仓订单存在且属于当前用户
        LockOrder lockOrder = this.getById(lockOrderId);
        BizAssert.notNull(lockOrder, () -> Errors.LOCK_ORDER_NOT_FOUND);
        BizAssert.isTrue(lockOrder.getUserId().equals(userId), () -> Errors.LOCK_ORDER_PERMISSION_DENIED);
        BizAssert.isTrue(lockOrder.getStatus() == 1, () -> Errors.LOCK_ORDER_STATUS_INVALID);

        // 2. 验证可提取金额
        BigDecimal availableAmount = lockOrder.getAvailableAmount() != null ?
            lockOrder.getAvailableAmount() : BigDecimal.ZERO;
        BizAssert.isTrue(availableAmount.compareTo(amount) >= 0, () -> Errors.LOCK_ORDER_AVAILABLE_INSUFFICIENT);

        // 3. 扣减可提取金额
        boolean updateResult = this.update(
            new LambdaUpdateWrapper<LockOrder>()
                .eq(LockOrder::getId, lockOrderId)
                .setSql("available_amount = available_amount - {0}", amount)
                .apply("available_amount - {0} >= 0", amount)
        );
        BizAssert.isTrue(updateResult, () -> Errors.WITHDRAW_OPERATION_FAILED);

        // 4. 记录日志
        recordLockOrderLog(userId, UserLogType.LOCK_RELEASE_LINEAR.getValue(),
            amount, "提取锁仓释放", lockOrderId, lockOrder.getSourceType());

        log.info("用户 {} 提取锁仓释放成功，订单ID：{}, 金额：{}", userId, lockOrderId, amount);
        return amount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackWithdrawFailed(Long lockOrderId, BigDecimal amount) {
        LockOrder lockOrder = this.getById(lockOrderId);
        BizAssert.notNull(lockOrder, () -> Errors.LOCK_ORDER_NOT_FOUND);

        boolean updateResult = this.update(
            new LambdaUpdateWrapper<LockOrder>()
                .eq(LockOrder::getId, lockOrderId)
                .setSql("available_amount = available_amount + {0}", amount)
        );
        BizAssert.isTrue(updateResult, () -> Errors.WITHDRAW_OPERATION_FAILED);

        log.info("用户 {} 提现失败回滚成功，订单ID：{}, 金额：{}", lockOrder.getUserId(), lockOrderId, amount);
    }

    @Override
    public IPage<LockOrderVo> pageForAdmin(Page<?> page, Long userId, String orderNo, String address, Integer status, Integer sourceType, String tokenId) {
        // 构建查询条件，使用表别名
        QueryWrapper<LockOrder> wrapper = new QueryWrapper<LockOrder>()
                .apply("1=1")
                .eq(userId != null, "lo.user_id", userId)
                .like(orderNo != null && !orderNo.trim().isEmpty(), "lo.order_no", orderNo)
                .eq(status != null, "lo.status", status)
                .eq(sourceType != null, "lo.source_type", sourceType)
                .orderByDesc("lo.create_time");

        // 如果有用户地址条件，直接使用表别名
        if (address != null && !address.trim().isEmpty()) {
            wrapper.like("u.address", address.trim());
        }

        return baseMapper.pageForAdmin(page, wrapper);
    }
}