package com.aic.app.service.impl;

import com.aic.app.exception.Errors;
import com.aic.app.util.BizAssert;
import com.aic.app.form.WithdrawNormalProductForm;
import com.aic.app.mapper.UserProductMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.aic.app.util.Utils;
import com.aic.app.vo.BondPurchaseRecordVo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@Service
@Slf4j
public class IUserProductServiceImpl extends ServiceImpl<UserProductMapper, UserProduct> implements IUserProductService {

    @Resource
    private IWithdrawService withdrawService;


    @Override
    public <E extends IPage<UserProduct>> E page(E page, Wrapper<UserProduct> queryWrapper) {
        return getBaseMapper().page(page, queryWrapper);
    }

    @Override
    public BigDecimal sum() {
        UserProduct userProduct = this.getOne(new QueryWrapper<UserProduct>().select("IFNULL(SUM(power), 0) as power"));
        return userProduct.getPower();
    }

    @Override
    public UserProduct findUserProduct(String userId, int productId) {
        LambdaQueryWrapper<UserProduct> qw = new LambdaQueryWrapper<UserProduct>()
                .eq(UserProduct::getUserId, userId)
                .eq(UserProduct::getProductId, productId)
                .last("limit 1");
        return getOne(qw);
    }

    @Override
    public UserProduct getUserProduct(String uid, int id) {
        return getOne(new LambdaQueryWrapper<UserProduct>().eq(UserProduct::getUserId, uid).eq(UserProduct::getId, id));
    }

    @Override
    public void updateProduct6Rate() {
    }

    @Override
    public IPage<UserProduct> pageChildOrders(Page<UserProduct> page, Wrapper<UserProduct> qw) {
        return baseMapper.pageChildOrders(page, qw);
    }

    @Override
    public IPage<BondPurchaseRecordVo> pageBondPurchaseRecords(Page<BondPurchaseRecordVo> page, Wrapper<UserProduct> queryWrapper) {
        return getBaseMapper().pageBondPurchaseRecords(page, queryWrapper);
    }

    @Override
    public BigDecimal sumAmountByUserIdAndProductType(Long userId, Integer productType) {
        return getBaseMapper().sumAmountByUserIdAndProductType(userId, productType);
    }
}
