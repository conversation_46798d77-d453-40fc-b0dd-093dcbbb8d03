package com.aic.app.service.impl;

import com.aic.app.mapper.UserMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品购买服务实现
 */
@Service
@Slf4j
public class ProductPurchaseServiceImpl implements IProductPurchaseService {
    
    @Resource
    private IUserProductService userProductService;
    
    @Resource
    private IProductService productService;
    
    @Resource
    private ILockOrderService lockOrderService;
    
    @Resource
    private IUserLogService userLogService;

    @Resource
    private UserMapper userMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserProduct purchaseProduct(User user, Product product, BigDecimal amount, BigDecimal quantity) {
        // 1. 创建购买记录
        String orderNo = Utils.genOrderNo();
        UserProduct userProduct = new UserProduct(user.getId(), orderNo, product, amount);
        userProduct.setAmount(amount);
        userProduct.setQuantity(quantity);
        userProductService.save(userProduct);
        
        // 2. 通用购买后处理（更新已售数量、记录日志）
        afterProductPurchase(userProduct, product);
        
        // 3. 根据产品类型创建锁仓订单或特殊处理
        switch (product.getType()) {
            case 1:
            case 2:
            case 3:
                // 节点产品、普通商品、债券产品 - 创建锁仓订单
                lockOrderService.createLockOrderByProduct(userProduct, product, amount);
                break;
            case 4:
                // 城主商品 - 不创建锁仓订单，处理4倍收益次数
                processCityLordProduct(userProduct, product, amount);
                break;
            default:
                throw new IllegalArgumentException("不支持的产品类型: " + product.getType());
        }
        
        log.info("产品购买完成，用户ID: {}, 产品ID: {}, 金额: {}, 数量: {}", 
            user.getId(), product.getId(), amount, quantity);
        
        return userProduct;
    }
    
    @Override
    public void processCityLordProduct(UserProduct userProduct, Product product, BigDecimal amount) {
        // 城主商品不创建锁仓订单，而是增加用户的4倍收益次数
        // 根据文档：每个城主商品可有两次普通质押静态收益*4权益
        
        // 1. 记录城主商品购买日志
        String remark = String.format("购买城主商品 - 订单ID: %d, 产品类型: 4", userProduct.getId());
        UserLog userLog = new UserLog(userProduct.getUserId(), 
            UserLogType.CITY_LORD_PURCHASE.getValue(), 
            amount, 
            amount, 
            remark);
        userLog.setLastAmount(BigDecimal.ZERO);
        userLogService.save(userLog);
        
        // 2. 增加用户的4倍收益次数
        addUserQuadrupleRewardTimes(userProduct.getUserId(), 2); // 每个城主商品增加2次
        
        log.info("用户购买城主商品，增加4倍收益次数，UID = {}, 商品 = {}, 次数 = 2", 
            userProduct.getUserId(), product.getName());
    }
    
    @Override
    public void afterProductPurchase(UserProduct userProduct, Product product) {
        // 1. 更新产品已售数量
        updateProductSoldQuantity(product.getId(), userProduct.getQuantity());
        
        // 2. 记录产品购买日志
        String remark = String.format("购买产品: %s，数量: %s - 订单ID: %d, 产品类型: %d",
                                     product.getName(), userProduct.getQuantity(),
                                     userProduct.getId(), product.getType());
        
        UserLog userLog = new UserLog(userProduct.getUserId(), 
            UserLogType.PRODUCT_PURCHASE.getValue(), 
            userProduct.getAmount(), 
            userProduct.getAmount(), 
            remark);
        userLog.setLastAmount(BigDecimal.ZERO);
        userLogService.save(userLog);
    }
    
    @Override
    public void updateProductSoldQuantity(Long productId, BigDecimal quantity) {
        boolean updateResult = productService.update(
            new LambdaUpdateWrapper<Product>()
                .eq(Product::getId, productId)
                .setSql("sold = COALESCE(sold, 0) + " + quantity)
        );
        
        if (!updateResult) {
            throw new RuntimeException("更新产品已售数量失败");
        }
        
        log.info("更新产品已售数量成功，产品ID = {}, 增加数量 = {}", productId, quantity);
    }
    
    @Override
    public void addUserQuadrupleRewardTimes(Long userId, int times) {
        // 使用UserMapper增加用户的4倍收益次数
        int updateResult = userMapper.addQuadrupleRewardTimes(userId, times);

        if (updateResult != 1) {
            log.error("增加用户4倍收益次数失败，用户ID: {}, 次数: {}", userId, times);
            throw new RuntimeException("增加用户4倍收益次数失败");
        }

        // 记录增加4倍收益次数的日志
        String remark = String.format("增加4倍收益次数: %d次 - 用户ID: %d", times, userId);
        UserLog userLog = new UserLog(userId,
            UserLogType.QUADRUPLE_REWARD_ADD.getValue(),
            BigDecimal.ZERO,
            new BigDecimal(times),
            remark);
        userLog.setLastAmount(BigDecimal.ZERO);
        userLogService.save(userLog);

        log.info("增加用户4倍收益次数成功，用户ID: {}, 次数: {}", userId, times);
    }
}