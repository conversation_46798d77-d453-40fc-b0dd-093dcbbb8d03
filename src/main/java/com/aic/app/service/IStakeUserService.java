package com.aic.app.service;

import com.aic.app.form.StakeForm;
import com.aic.app.model.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

public interface IStakeUserService extends IService<StakeUser> {

    User checkUser(String address);

    /**
     * 领取收益
     *
     * @param user
     * @return
     */
    boolean withdraw(StakeUser user, String tokenId, BigDecimal amount);
    
    IPage<UserModel> findAllForAdminUser(Page<User> page, QueryWrapper<User> queryWrapper);
    
    IPage<StakeUserModel> findAllForAdmin(Page<StakeUser> page, QueryWrapper<StakeUser> queryWrapper);

    StakeUser getStakeUser(Long userId, String tokenId);

    void stake(User user, StakeForm form);

    /**
     * 提取质押静态池
     */
    BigDecimal withdrawStakeStaticPool(Long userId, String tokenId, BigDecimal amount);

    /**
     * 提取质押动态池
     */
    BigDecimal withdrawStakeDynamicPool(Long userId, String tokenId, BigDecimal amount);

    /**
     * 提取锁仓静态池
     */
    BigDecimal withdrawLockStaticPool(Long userId, String tokenId, BigDecimal amount);

    /**
     * 提取锁仓动态池
     */
    BigDecimal withdrawLockDynamicPool(Long userId, String tokenId, BigDecimal amount);

    void stakeFromWallet(Events events);

    void updateSuccess(RustService.CheckResult checkResult);

    void updateFails(RustService.CheckResult checkResult);

    StakeUser sumChilds(Long id);
}
