package com.aic.app.mapper;

import com.aic.app.model.LockOrder;
import com.aic.app.vo.LockOrderVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 锁仓订单Mapper
 */
@Mapper
public interface LockOrderMapper extends BaseMapper<LockOrder> {
    
    /**
     * 获取用户锁仓总额
     */
    @Select("SELECT COALESCE(SUM(lock_amount), 0) FROM lock_order WHERE user_id = #{userId} AND token_id = #{tokenId} AND status = 1")
    BigDecimal getUserTotalLockAmount(@Param("userId") Long userId, @Param("tokenId") String tokenId);
    
    /**
     * 获取用户可提取金额
     */
    @Select("SELECT COALESCE(SUM(available_amount), 0) FROM lock_order WHERE user_id = #{userId} AND token_id = #{tokenId} AND status = 1")
    BigDecimal getUserAvailableAmount(@Param("userId") Long userId, @Param("tokenId") String tokenId);
    
    /**
     * 获取需要线性释放的订单
     */
    @Select("SELECT * FROM lock_order WHERE status = 1 AND released_days < total_days")
    List<LockOrder> getLinearReleaseOrders();
    
    /**
     * 获取启用静态收益的订单
     */
    @Select("SELECT * FROM lock_order WHERE status = 1")
    List<LockOrder> getStaticRewardOrders();
    
    /**
     * 获取启用动态收益的订单
     */
    @Select("SELECT * FROM lock_order WHERE status = 1")
    List<LockOrder> getDynamicRewardOrders();
    
    /**
     * Get total lock amount for specific token
     */
    @Select("SELECT COALESCE(SUM(lock_amount), 0) FROM lock_order WHERE token_id = #{tokenId} AND status = 1")
    BigDecimal getTotalLockAmount(@Param("tokenId") String tokenId);
    
    /**
     * 获取用户锁仓 USD 总金额
     */
    @Select("SELECT COALESCE(SUM(usd_amount), 0) FROM lock_order WHERE user_id = #{userId} AND status = 1")
    BigDecimal getUserTotalUsdAmount(@Param("userId") Long userId);
    
    /**
     * 获取用户列表的锁仓 USD 总金额
     */
    @Select("SELECT COALESCE(SUM(usd_amount), 0) FROM lock_order WHERE user_id IN (${userIds}) AND status = 1")
    BigDecimal getUserListTotalUsdAmount(@Param("userIds") String userIds);
    
    /**
     * 管理员分页查询锁仓订单（包含用户地址）
     */
    @Select("SELECT lo.id, lo.order_no, lo.user_id, lo.source_type, lo.source_id, " +
            "lo.token_id, lo.lock_amount, lo.released_amount, lo.available_amount, " +
            "lo.total_days, lo.released_days, lo.daily_release_amount, lo.status, " +
            "lo.create_time, lo.update_time, u.address " +
            "FROM lock_order lo " +
            "LEFT JOIN (select id, address from user) u ON lo.user_id = u.id " +
            "${ew.customSqlSegment}")
    IPage<LockOrderVo> pageForAdmin(Page<?> page, @Param("ew") com.baomidou.mybatisplus.core.conditions.Wrapper<LockOrder> queryWrapper);
}