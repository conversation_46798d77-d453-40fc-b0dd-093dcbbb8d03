package com.aic.app.admin;

import com.aic.app.service.ILockOrderService;
import com.aic.app.vo.LockOrderVo;
import com.aic.app.vo.Result;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 锁仓订单管理控制器
 */
@RestController
@RequestMapping("/admin")
@AllArgsConstructor
@Slf4j
@Hidden
@Tag(name = "锁仓订单管理", description = "管理员锁仓订单相关接口")
public class LockOrderAdminController {

    private final ILockOrderService lockOrderService;

    @GetMapping("/lock-orders")
    @Operation(summary = "管理员查询锁仓订单列表")
    public Result<IPage<LockOrderVo>> getLockOrdersForAdmin(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String orderNo,
            @RequestParam(required = false) String address,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer sourceType,
            @RequestParam(required = false) String tokenId) {

        log.info("管理员查询锁仓订单，参数：page={}, size={}, userId={}, orderNo={}, address={}, status={}, sourceType={}, tokenId={}",
                page, size, userId, orderNo, address, status, sourceType, tokenId);

        // 分页查询
        IPage<LockOrderVo> result = lockOrderService.pageForAdmin(new Page<>(page, size), userId, orderNo, address, status, sourceType, tokenId);
        
        log.info("管理员锁仓订单查询完成，共 {} 条记录", result.getTotal());
        
        return Result.success(result);
    }
}