package com.aic.app.job;

import com.aic.app.mapper.LockOrderMapper;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.model.AssetEnum;
import com.aic.app.service.IChartService;
import com.aic.app.service.IProductService;
import com.aic.app.service.ISysConfigService;
import com.aic.app.service.IStakeUserService;
import com.aic.app.service.ILockOrderService;
import com.aic.app.service.RustService;
import com.aic.app.util.JsonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Stats related scheduled tasks
 */
//@Component
@Slf4j
public class StatsJob {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    @Resource
    private IProductService productService;
    
    @Resource
    private StakeUserMapper stakeUserMapper;
    
    @Resource
    private LockOrderMapper lockOrderMapper;
    
    @Resource
    private ISysConfigService sysConfigService;
    
    @Resource
    private IChartService chartService;
    
    @Resource
    private IStakeUserService stakeUserService;
    
    @Resource
    private ILockOrderService lockOrderService;

    /**
     * Save XYC stats to Redis every minute
     * key: xyc:stats
     * fields: xycPrice, burnAmount, totalSupply, totalStakeAmount
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void saveXycPriceToRedis() {
        log.info("Starting to save XYC stats to Redis");
        
        try {
            // Get XYC price
            BigDecimal xycPrice = productService.getPrice(AssetEnum.XYC.getTokenId());
            
            if (xycPrice != null && xycPrice.compareTo(BigDecimal.ZERO) > 0) {
                // Save XYC price to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "xycPrice", xycPrice.toPlainString());
                log.info("Successfully saved XYC price to Redis: {}", xycPrice.toPlainString());
            } else {
                log.warn("Invalid XYC price obtained: {}", xycPrice);
            }
            
        } catch (Exception e) {
            log.error("Failed to save XYC price to Redis", e);
        }
        
        try {
            // Get total stake amount (confirmed + pending) for XYC
            String xycTokenId = AssetEnum.XYC.getTokenId();
            BigDecimal totalStakeAmount = stakeUserMapper.getTotalStakeAmount(xycTokenId);
            if (totalStakeAmount == null) {
                totalStakeAmount = BigDecimal.ZERO;
            }
            
            // Save total stake amount to Redis Hash
            stringRedisTemplate.opsForHash().put("xyc:stats", "totalStakeAmount", totalStakeAmount.toPlainString());
            log.info("Successfully saved total stake amount to Redis: {}", totalStakeAmount.toPlainString());
            
        } catch (Exception e) {
            log.error("Failed to save total stake amount to Redis", e);
        }
        
        try {
            // Get XYC burn amount
            String burnAmountStr = RustService.getXycBurnAmount();
            if (burnAmountStr != null && !burnAmountStr.trim().isEmpty()) {
                // Save burn amount to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "burnAmount", burnAmountStr);
                log.info("Successfully saved XYC burn amount to Redis: {}", burnAmountStr);
            } else {
                log.warn("Invalid XYC burn amount obtained: {}", burnAmountStr);
            }
            
        } catch (Exception e) {
            log.error("Failed to save XYC burn amount to Redis", e);
        }
        
        try {
            // Get XYC total supply
            String totalSupplyStr = RustService.getXycTotalSupply();
            if (totalSupplyStr != null && !totalSupplyStr.trim().isEmpty()) {
                // Save total supply to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "totalSupply", totalSupplyStr);
                log.info("Successfully saved XYC total supply to Redis: {}", totalSupplyStr);
            } else {
                log.warn("Invalid XYC total supply obtained: {}", totalSupplyStr);
            }
            
        } catch (Exception e) {
            log.error("Failed to save XYC total supply to Redis", e);
        }
        
        try {
            // Get LP value
            String lpValueStr = RustService.getTotalLpValue();
            if (lpValueStr != null && !lpValueStr.trim().isEmpty()) {
                // Save LP value to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "lpValue", lpValueStr);
                log.info("Successfully saved LP value to Redis: {}", lpValueStr);
            } else {
                log.warn("Invalid LP value obtained: {}", lpValueStr);
            }
            
        } catch (Exception e) {
            log.error("Failed to save LP value to Redis", e);
        }
    }

    /**
     * Save XYC stake principal and APY to Redis every 5 minutes
     * Includes confirmed + pending stake amount + lock amount
     * key: xyc:stats
     * fields: stakePrincipal, apy
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void saveXycStakePrincipalToRedis() {
        log.info("Starting to save XYC stake principal and APY to Redis");
        
        try {
            String xycTokenId = AssetEnum.XYC.getTokenId();
            
            // Get total stake amount (confirmed + pending)
            BigDecimal totalStakeAmount = stakeUserMapper.getTotalStakeAmount(xycTokenId);
            if (totalStakeAmount == null) {
                totalStakeAmount = BigDecimal.ZERO;
            }
            
            // Get total lock amount
            BigDecimal totalLockAmount = lockOrderMapper.getTotalLockAmount(xycTokenId);
            if (totalLockAmount == null) {
                totalLockAmount = BigDecimal.ZERO;
            }
            
            // Calculate total principal
            BigDecimal totalPrincipal = totalStakeAmount.add(totalLockAmount);
            
            // Save stake principal to Redis Hash
            stringRedisTemplate.opsForHash().put("xyc:stats", "stakePrincipal", totalPrincipal.toPlainString());
            
            log.info("Successfully saved XYC stake principal to Redis: stake={}, lock={}, total={}", 
                    totalStakeAmount.toPlainString(), 
                    totalLockAmount.toPlainString(), 
                    totalPrincipal.toPlainString());
            
        } catch (Exception e) {
            log.error("Failed to save XYC stake principal to Redis", e);
        }
        
        try {
            // Get static reward rate from SysConfig
            var sysConfig = sysConfigService.getSysConfig();
            if (sysConfig != null && sysConfig.getRewardRate() != null) {
                BigDecimal staticRate = sysConfig.getRewardRate();
                
                // Calculate APY = (1 + staticRate)^730
                // Using Math.pow for the calculation
                double rate = staticRate.doubleValue();
                double apy = (1 + rate) * 730;
                BigDecimal apyDecimal = BigDecimal.valueOf(apy);
                
                // Save APY to Redis Hash
                stringRedisTemplate.opsForHash().put("xyc:stats", "apy", apyDecimal.toPlainString());
                
                log.info("Successfully saved APY to Redis: staticRate={}, apy={}", 
                        staticRate.toPlainString(), 
                        apyDecimal.toPlainString());
            } else {
                log.warn("SysConfig or rewardRate is null, cannot calculate APY");
            }
            
        } catch (Exception e) {
            log.error("Failed to save APY to Redis", e);
        }
    }

    /**
     * Hourly statistics task
     * 1. Save dashboard total stake amount and unstaked amount to Chart table data field, type=1
     * 2. Save available amounts + lock release amounts + four static/dynamic pools to Chart table value field, type=2
     * 3. Save burn amount to Chart table value field, type=3
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void hourlyStatsTask() {
        log.info("Starting hourly statistics task");
        // 获取当前小时的时间戳（去掉分钟和秒）
        Long currentHourTimestamp = (System.currentTimeMillis() / 1000 / 3600) * 3600;
        String xycTokenId = AssetEnum.XYC.getTokenId();
        
        try {
            // Task 1: Save dashboard stats to Chart table data field, type=1
            saveDashboardStatsToChart(currentHourTimestamp, xycTokenId);
            
            // Task 2: Save available amounts to Chart table value field, type=2
            saveAvailableAmountsToChart(currentHourTimestamp, xycTokenId);
            
            // Task 3: Save burn amount to Chart table value field, type=3
            saveBurnAmountToChart(currentHourTimestamp);
            
            log.info("Hourly statistics task completed");
            
        } catch (Exception e) {
            log.error("Hourly statistics task failed", e);
        }
    }
    
    /**
     * Task 1: Save dashboard total stake amount and unstaked amount to Chart table data field, type=1
     */
    private void saveDashboardStatsToChart(Long currentHourTimestamp, String xycTokenId) {
        try {
            // Get total stake amount
            BigDecimal totalStakeAmount = stakeUserMapper.getTotalStakeAmount(xycTokenId);
            if (totalStakeAmount == null) {
                totalStakeAmount = BigDecimal.ZERO;
            }
            
            // Get total supply
            String totalSupplyStr = RustService.getXycTotalSupply();
            BigDecimal totalSupply = BigDecimal.ZERO;
            if (totalSupplyStr != null && !totalSupplyStr.trim().isEmpty()) {
                totalSupply = new BigDecimal(totalSupplyStr);
            }
            
            // Get burn amount
            String burnAmountStr = RustService.getXycBurnAmount();
            BigDecimal burnAmount = BigDecimal.ZERO;
            if (burnAmountStr != null && !burnAmountStr.trim().isEmpty()) {
                burnAmount = new BigDecimal(burnAmountStr);
            }
            
            // Calculate unstaked amount = total - burn - stake
            BigDecimal totalUnstakedAmount = totalSupply.subtract(burnAmount).subtract(totalStakeAmount);
            if (totalUnstakedAmount.compareTo(BigDecimal.ZERO) < 0) {
                totalUnstakedAmount = BigDecimal.ZERO;
            }
            
            // Build JSON data - only save the two main dashboard fields
            Map<String, Object> dashboardData = new HashMap<>();
            dashboardData.put("stake", totalStakeAmount.stripTrailingZeros().toPlainString());
            dashboardData.put("unstaked", totalUnstakedAmount.stripTrailingZeros().toPlainString());
            
            String jsonData = JsonUtils.toJson(dashboardData);
            
            // Save to Chart table, type=1, value=0 (main data in data field)
            boolean result = chartService.addChartData(1, currentHourTimestamp, BigDecimal.ZERO, jsonData);
            
            if (result) {
                log.info("Dashboard stats saved successfully, hour timestamp: {}, total stake: {}, unstaked: {}", 
                        currentHourTimestamp, totalStakeAmount.stripTrailingZeros().toPlainString(), totalUnstakedAmount.stripTrailingZeros().toPlainString());
            } else {
                log.error("Failed to save dashboard stats");
            }
            
        } catch (Exception e) {
            log.error("Failed to save dashboard stats", e);
        }
    }
    
    /**
     * Task 2: Save available amounts + lock release amounts + four static/dynamic pools to Chart table value field, type=2
     */
    private void saveAvailableAmountsToChart(Long currentHourTimestamp, String xycTokenId) {
        try {
            BigDecimal totalAvailableAmount = BigDecimal.ZERO;
            
            // 1. Get current stake amount (confirmed + pending)
            BigDecimal currentStakeAmount = stakeUserMapper.getTotalStakeAmount(xycTokenId);
            if (currentStakeAmount == null) {
                currentStakeAmount = BigDecimal.ZERO;
            }
            
            // 2. Get lock release pending amount
            BigDecimal lockReleaseAmount = BigDecimal.ZERO;
            try {
                lockReleaseAmount = lockOrderMapper.getTotalLockAmount(xycTokenId);
                if (lockReleaseAmount == null) {
                    lockReleaseAmount = BigDecimal.ZERO;
                }
            } catch (Exception e) {
                log.warn("Failed to get lock release amount", e);
            }
            
            // 3. Get four static/dynamic pools total from stake_user table
            BigDecimal totalStaticPool = BigDecimal.ZERO;
            BigDecimal totalDynamicPool = BigDecimal.ZERO;
            BigDecimal totalLockStaticPool = BigDecimal.ZERO;
            BigDecimal totalLockDynamicPool = BigDecimal.ZERO;
            
            try {
                // Get stake static and dynamic pools total
                totalStaticPool = stakeUserMapper.getTotalStaticPool(xycTokenId);
                if (totalStaticPool == null) {
                    totalStaticPool = BigDecimal.ZERO;
                }
                
                totalDynamicPool = stakeUserMapper.getTotalDynamicPool(xycTokenId);
                if (totalDynamicPool == null) {
                    totalDynamicPool = BigDecimal.ZERO;
                }
                
                // Get lock static and dynamic pools total
                totalLockStaticPool = stakeUserMapper.getTotalLockStaticPool(xycTokenId);
                if (totalLockStaticPool == null) {
                    totalLockStaticPool = BigDecimal.ZERO;
                }
                
                totalLockDynamicPool = stakeUserMapper.getTotalLockDynamicPool(xycTokenId);
                if (totalLockDynamicPool == null) {
                    totalLockDynamicPool = BigDecimal.ZERO;
                }
                
                log.info("Four pools total - staticPool: {}, dynamicPool: {}, lockStaticPool: {}, lockDynamicPool: {}", 
                        totalStaticPool.toPlainString(), totalDynamicPool.toPlainString(), 
                        totalLockStaticPool.toPlainString(), totalLockDynamicPool.toPlainString());
                
            } catch (Exception e) {
                log.warn("Failed to get static/dynamic pools total", e);
            }
            
            // Calculate total amount = current stake + lock release + four pools
            totalAvailableAmount = currentStakeAmount
                    .add(lockReleaseAmount)
                    .add(totalStaticPool)
                    .add(totalDynamicPool)
                    .add(totalLockStaticPool)
                    .add(totalLockDynamicPool);
                    
            log.info("Total amount calculation - currentStake: {}, lockRelease: {}, staticPool: {}, dynamicPool: {}, lockStaticPool: {}, lockDynamicPool: {}, total: {}", 
                    currentStakeAmount.toPlainString(), lockReleaseAmount.toPlainString(),
                    totalStaticPool.toPlainString(), totalDynamicPool.toPlainString(), 
                    totalLockStaticPool.toPlainString(), totalLockDynamicPool.toPlainString(),
                    totalAvailableAmount.toPlainString());
            
            // Save to Chart table, type=2
            boolean result = chartService.addChartData(2, currentHourTimestamp, totalAvailableAmount);
            
            if (result) {
                log.info("Total amounts saved successfully, hour timestamp: {}, total: {}", 
                        currentHourTimestamp, totalAvailableAmount.stripTrailingZeros().toPlainString());
            } else {
                log.error("Failed to save total amounts");
            }
            
        } catch (Exception e) {
            log.error("Failed to save available amounts", e);
        }
    }
    
    /**
     * Task 3: Save burn amount to Chart table value field, type=3
     */
    private void saveBurnAmountToChart(Long currentHourTimestamp) {
        try {
            // Get XYC burn amount
            String burnAmountStr = RustService.getXycBurnAmount();
            BigDecimal burnAmount = BigDecimal.ZERO;
            
            if (burnAmountStr != null && !burnAmountStr.trim().isEmpty()) {
                burnAmount = new BigDecimal(burnAmountStr);
            }
            
            // Save to Chart table, type=3
            boolean result = chartService.addChartData(3, currentHourTimestamp, burnAmount);
            
            if (result) {
                log.info("Burn amount saved successfully, hour timestamp: {}, amount: {}", 
                        currentHourTimestamp, burnAmount.stripTrailingZeros().toPlainString());
            } else {
                log.error("Failed to save burn amount");
            }
            
        } catch (Exception e) {
            log.error("Failed to save burn amount", e);
        }
    }
}