package com.aic.app.job;

import com.aic.app.service.*;
import com.aic.app.util.Utils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WithdrawJob {

    @Resource
    IStakeUserService stakeUserService;
    @Value("${spring.profiles.active:default}")
    String profile;

    @PostConstruct
    public void init() {
        if ("default".equals(profile)) {
            return;
        }
        Thread thread = new Thread(this::run);
        thread.start();
    }

    public void run() {
        log.info("[job9] run");
        for (;;) {
            try {
                execute();
            } catch (Exception e) {
                log.error("[job9] run error", e);
            }
            sleep(5000);
        }
    }

    public void execute() {
        List<RustService.CheckResult> results = RustService.checkWithdrawVo();
        for (RustService.CheckResult result : results) {
            if (Utils.isCloseTime()) {
                break;
            }
            if (result.isResult()) {
                // 已成功
                stakeUserService.updateSuccess(result);
            } else {
                stakeUserService.updateFails(result);
            }
        }
    }

    // 睡眠
    public void sleep(long millis) {
        try {
//            log.info("睡眠15秒");
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
