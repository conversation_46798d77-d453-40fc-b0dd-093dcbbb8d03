package com.aic.app.job;

import com.aic.app.mapper.LockOrderMapper;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserMapper;
import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统一结算任务Job30 - 整合Job3和LockOrderJob功能
 * 功能：
 * 1. 更新用户等级
 * 2. 质押收益结算（静态收益*4、动态收益：分享奖励、社区奖励、平级奖）
 * 3. 锁仓订单收益结算（先释放，再结算，静态收益*2，动态收益规则与质押一致）
 * 4. 城主加成（每次结算最多消耗一次quadrupleRewardTimes）
 * 5. 更新全网质押量和当前指数
 */
@Component
@Slf4j
public class Job30 {

    @Resource
    private StakeUserMapper stakeUserMapper;
    
    @Resource
    private UserMapper userMapper;
    
    @Resource
    private UserRelationMapper userRelationMapper;
    
    @Resource
    private LockOrderMapper lockOrderMapper;
    
    @Resource
    private ISysConfigService sysConfigService;
    
    @Resource
    private IUserLogService userLogService;
    
    @Resource
    private ILockOrderService lockOrderService;
    
    @Resource
    private IProductService productService;
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IUserStakeService userStakeService;

    /**
     * 早上8点执行 - 结算 + 更新全网质押量
     */
//    @Scheduled(cron = "0 0 8 * * ?")
    public void runMorning() {
        log.info("开始执行早上8点统一结算任务Job30");
        
        try {
            // 1. 执行核心结算逻辑（完整事务）
            executeSettlementTransaction();
            
            // 2. 更新全网质押量（独立事务，不影响结算）
            updateTotalAmount();
            
            log.info("早上8点统一结算任务Job30执行完成");
        } catch (Exception e) {
            log.error("早上8点统一结算任务Job30执行失败", e);
            throw e;
        }
    }
    
    /**
     * 晚上8点执行 - 结算 + 更新全网质押量 + 更新当前指数
     */
//    @Scheduled(cron = "0 0 20 * * ?")
    public void runEvening() {
        log.info("开始执行晚上8点统一结算任务Job30");
        
        try {
            // 1. 执行核心结算逻辑（完整事务）
            executeSettlementTransaction();
            
            // 2. 更新全网质押量（独立事务，不影响结算）
            updateTotalAmount();
            
            // 3. 更新当前指数（独立事务，不影响结算）
            updateCurrentIndex();
            
            log.info("晚上8点统一结算任务Job30执行完成");
        } catch (Exception e) {
            log.error("晚上8点统一结算任务Job30执行失败", e);
            throw e;
        }
    }
    
    /**
     * 结算事务包装方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeSettlementTransaction() {
        executeSettlement();
    }
    
    /**
     * 核心结算逻辑 - 早晚8点都会执行（完整事务）
     */
    private void executeSettlement() {
        log.info("开始执行核心结算逻辑");
        
        // 1. 更新用户等级（批量更新User表）
        updateUserLevels();
        
        // 2. 质押功能结算
        processStakeSettlement();
        
        // 3. 锁仓订单结算  
        processLockOrderSettlement();
        
        log.info("核心结算逻辑执行完成");
    }

    /**
     * 1. 更新用户等级 - 批量更新User表
     */
    private void updateUserLevels() {
        log.info("开始更新用户等级");
        
        // 收集需要更新的用户等级信息
        List<UserLevelUpdate> levelUpdates = new ArrayList<>();
        
        // 查询所有有锁仓订单的用户
        List<LockOrder> activeLockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1)  // status=1 锁仓中
        );
        
        // 按用户分组，获取有锁仓订单的用户列表
        Set<Long> lockOrderUserIds = activeLockOrders.stream()
            .map(LockOrder::getUserId)
            .collect(Collectors.toSet());
        
        log.debug("找到{}个有锁仓订单的用户", lockOrderUserIds.size());
        
        for (Long userId : lockOrderUserIds) {
            // 计算个人锁仓 USD 金额
            BigDecimal personalLockUsd = lockOrderMapper.getUserTotalUsdAmount(userId);
            
            // 计算社区业绩（团队锁仓 USD 总金额）
            BigDecimal communityPerf = getCommunityLockUsdPerformance(userId);
            
            // 根据锁仓 USD 金额确定用户等级
            int newLevel = getUserLevel(personalLockUsd, communityPerf, userId);
            
            // 检查是否需要更新
            User currentUser = userMapper.selectById(userId);
            if (currentUser != null) {
                log.debug("用户{}等级检查：当前等级={}, 个人锁仓USD={}, 社区锁仓USD业绩={}, 计算等级={}", 
                    userId, currentUser.getLevel(), personalLockUsd, communityPerf, newLevel);
                
                if (currentUser.getLevel() != newLevel) {
                    levelUpdates.add(new UserLevelUpdate(userId, newLevel));
                    log.debug("用户{}需要等级更新：{} -> {}", userId, currentUser.getLevel(), newLevel);
                }
            }
        }
        
        // 批量更新User表的level字段
        if (!levelUpdates.isEmpty()) {
            batchUpdateUserLevels(levelUpdates);
            log.info("批量更新用户等级完成，更新{}个用户", levelUpdates.size());
        } else {
            log.info("没有用户需要等级更新");
        }
    }

    /**
     * 2. 质押功能结算
     */
    private void processStakeSettlement() {
        log.info("开始质押功能结算");
        
        // 2.1 T+1结算：待确认质押转为已确认质押
        settlePendingStake();
        
        // 2.2 计算质押静态收益（支持复利 + 城主加成）
        calculateStakeStaticRewards();
        
        // 2.3 计算质押动态收益（分别记录流水）
        calculateStakeDynamicRewards();
    }

    /**
     * 3. 锁仓订单结算
     */
    private void processLockOrderSettlement() {
        log.info("开始锁仓订单结算");
        
        // 3.1 先释放：线性释放处理
        processLockOrderRelease();
        
        // 3.2 再结算：计算锁仓静态收益（2倍收益率 + 城主加成）
        calculateLockOrderStaticRewards();
        
        // 3.3 计算锁仓动态收益（规则与质押一致，分别记录流水）
        calculateLockOrderDynamicRewards();
    }

    /**
     * T+1结算：将待确认质押转为已确认质押
     */
    private void settlePendingStake() {
        log.info("开始T+1结算");
        
        // 查询所有有待确认质押的用户
        List<StakeUser> pendingUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getPendingAmount, BigDecimal.ZERO)
        );
        
        for (StakeUser stakeUser : pendingUsers) {
            BigDecimal pendingAmount = Optional.ofNullable(stakeUser.getPendingAmount()).orElse(BigDecimal.ZERO);
            if (pendingAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal currentAmount = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
                // 将待确认转为已确认
                stakeUserMapper.update(null,
                    new LambdaUpdateWrapper<StakeUser>()
                        .eq(StakeUser::getId, stakeUser.getId())
                        .set(StakeUser::getCurrentAmount, currentAmount.add(pendingAmount))
                        .set(StakeUser::getPendingAmount, BigDecimal.ZERO)
                );
                
                log.info("用户{}待确认质押{}转为已确认", stakeUser.getUserId(), pendingAmount);
            }
        }
        // 质押记录更新为已确认
        userStakeService.update(null,
            new LambdaUpdateWrapper<UserStake>()
                .set(UserStake::getStatus, 1) // 1-已确认
                .eq(UserStake::getStatus, 0) // 0-待确认
                .eq(UserStake::getType, 0) // 0-质押操作（非解除质押）
        );
        
        log.info("T+1结算完成，处理{}个用户", pendingUsers.size());
    }

    /**
     * 计算质押静态收益（支持城主加成和复利）
     */
    private void calculateStakeStaticRewards() {
        log.info("开始计算质押静态收益");
        
        // 获取系统配置的收益率
        SysConfig sysConfig = sysConfigService.getSysConfig();
        BigDecimal rewardRate = sysConfig.getRewardRate();
        if (rewardRate == null || rewardRate.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("收益率配置异常，跳过质押静态收益计算");
            return;
        }
        
        // 查询所有有已确认质押的用户
        List<StakeUser> stakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getCurrentAmount, BigDecimal.ZERO)
        );
        
        for (StakeUser stakeUser : stakeUsers) {
            BigDecimal currentAmount = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
            BigDecimal staticPool = Optional.ofNullable(stakeUser.getStaticPool()).orElse(BigDecimal.ZERO);

            // 静态复利：计算收益基数 = 已确认质押 + 静态池子
            BigDecimal rewardBase = currentAmount.add(staticPool);

            // 计算基础静态收益 = (已确认质押 + 静态池子) * 收益率
            BigDecimal baseStaticReward = rewardBase.multiply(rewardRate)
                .setScale(8, RoundingMode.HALF_UP);

            if (baseStaticReward.compareTo(BigDecimal.ZERO) > 0) {
                // 城主加成处理（每次结算最多消耗一次）
                BigDecimal finalStaticReward = applyCityLordBonus(stakeUser.getUserId(), baseStaticReward);
                
                BigDecimal totalStatic = Optional.ofNullable(stakeUser.getTotalStatic()).orElse(BigDecimal.ZERO);

                // 更新静态池子和今日静态、累计静态（静态复利）
                stakeUserMapper.update(null,
                    new LambdaUpdateWrapper<StakeUser>()
                        .eq(StakeUser::getId, stakeUser.getId())
                        .set(StakeUser::getStaticPool, staticPool.add(finalStaticReward))
                        .set(StakeUser::getTodayStatic, finalStaticReward)
                        .set(StakeUser::getTotalStatic, totalStatic.add(finalStaticReward))
                );
                
                // 记录质押静态收益流水
                userLogService.addLog(stakeUser.getUserId(),
                    UserLogType.StakeProfit.getValue(),
                    finalStaticReward,
                    "质押静态收益");
                
                log.debug("用户{}质押静态收益：质押金额={}, 静态池={}, 收益基数={}, 收益={}",
                    stakeUser.getUserId(), currentAmount, staticPool, rewardBase, finalStaticReward);
            }
        }
        
        log.info("质押静态收益计算完成，处理{}个用户", stakeUsers.size());
    }

    /**
     * 处理锁仓订单线性释放
     */
    private void processLockOrderRelease() {
        log.info("开始处理锁仓订单线性释放");

        // 查询所有状态为锁仓中的订单
        List<LockOrder> activeLockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1)  // status=1 锁仓中
        );

        Date today = new Date();
        java.sql.Date sqlToday = new java.sql.Date(today.getTime());

        for (LockOrder lockOrder : activeLockOrders) {
            try {
                processLinearRelease(lockOrder, sqlToday);
            } catch (Exception e) {
                log.error("处理锁仓订单释放失败，订单号={}", lockOrder.getOrderNo(), e);
            }
        }

        log.info("锁仓订单线性释放处理完成，处理{}个订单", activeLockOrders.size());
    }

    /**
     * 计算锁仓订单静态收益（2倍收益率 + 城主加成）
     */
    private void calculateLockOrderStaticRewards() {
        log.info("开始计算锁仓订单静态收益");

        // 获取系统配置的收益率
        SysConfig sysConfig = sysConfigService.getSysConfig();
        BigDecimal rewardRate = sysConfig.getRewardRate();
        if (rewardRate == null || rewardRate.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("收益率配置异常，跳过锁仓订单静态收益计算");
            return;
        }

        // 查询所有状态为锁仓中的订单
        List<LockOrder> activeLockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1)  // status=1 锁仓中
        );

        for (LockOrder lockOrder : activeLockOrders) {
            BigDecimal availableAmount = Optional.ofNullable(lockOrder.getAvailableAmount()).orElse(BigDecimal.ZERO);

            if (availableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                // 没有可提取金额，无需计算收益
                continue;
            }

            // 锁仓订单静态收益 = 已释放未提取金额 * 收益率 * 2
            BigDecimal baseLockStaticReward = availableAmount.multiply(rewardRate).multiply(new BigDecimal("2"))
                .setScale(8, RoundingMode.HALF_UP);

            if (baseLockStaticReward.compareTo(BigDecimal.ZERO) > 0) {
                // 城主加成处理（每次结算最多消耗一次）
                BigDecimal finalLockStaticReward = applyCityLordBonus(lockOrder.getUserId(), baseLockStaticReward);

                // 获取或创建用户的StakeUser记录
                StakeUser stakeUser = getOrCreateStakeUser(lockOrder.getUserId());

                BigDecimal currentLockStaticPool = Optional.ofNullable(stakeUser.getLockStaticPool()).orElse(BigDecimal.ZERO);
                BigDecimal currentTotalStatic = Optional.ofNullable(stakeUser.getTotalStatic()).orElse(BigDecimal.ZERO);

                // 更新用户锁仓静态池和累计静态收益
                stakeUserMapper.update(null,
                    new LambdaUpdateWrapper<StakeUser>()
                        .eq(StakeUser::getId, stakeUser.getId())
                        .set(StakeUser::getLockStaticPool, currentLockStaticPool.add(finalLockStaticReward))
                        .set(StakeUser::getTotalStatic, currentTotalStatic.add(finalLockStaticReward))
                );

                // 记录锁仓静态收益流水（与质押分开）
                userLogService.addLog(lockOrder.getUserId(),
                    UserLogType.StakeProfit.getValue(),
                    finalLockStaticReward,
                    "锁仓订单静态收益-订单号:" + lockOrder.getOrderNo());

                log.debug("锁仓订单静态收益，订单号={}, 可提取金额={}, 收益={}",
                    lockOrder.getOrderNo(), availableAmount, finalLockStaticReward);
            }
        }

        log.info("锁仓订单静态收益计算完成");
    }

    /**
     * 城主加成处理 - 每次结算最多消耗一次
     */
    private BigDecimal applyCityLordBonus(Long userId, BigDecimal baseReward) {
        // 检查用户剩余4倍收益次数
        User user = userMapper.selectById(userId);
        if (user == null) {
            return baseReward;
        }
        
        int remainingTimes = user.getQuadrupleRewardTimes();
        
        if (remainingTimes > 0) {
            // 每次结算最多消耗一次
            userMapper.update(null,
                new LambdaUpdateWrapper<User>()
                    .eq(User::getId, userId)
                    .set(User::getQuadrupleRewardTimes, remainingTimes - 1)
            );
            
            // 返回4倍收益
            BigDecimal bonusReward = baseReward.multiply(BigDecimal.valueOf(4));
            
            // 记录城主加成日志
            userLogService.addLog(userId,
                UserLogType.StakeProfit.getValue(),
                baseReward.multiply(BigDecimal.valueOf(3)), // 额外的3倍收益
                "城主加成4倍收益");
            
            log.debug("用户{}使用城主加成，基础收益={}, 4倍收益={}, 剩余次数={}",
                userId, baseReward, bonusReward, remainingTimes - 1);
            
            return bonusReward;
        }
        
        return baseReward;
    }

    // 辅助类：用户等级更新信息
    private static class UserLevelUpdate {
        private final Long userId;
        private final int newLevel;
        
        public UserLevelUpdate(Long userId, int newLevel) {
            this.userId = userId;
            this.newLevel = newLevel;
        }
        
        public Long getUserId() { return userId; }
        public int getNewLevel() { return newLevel; }
    }

    /**
     * 计算质押动态收益（分别记录流水）
     */
    private void calculateStakeDynamicRewards() {
        log.info("开始计算质押动态收益");
        
        // 分享奖励
        calculateStakeShareRewards();
        
        // 社区奖励（含烧伤机制，按照Job3的实现逻辑）
        calculateStakeCommunityRewards();
        
        // 平级奖励（V4-V6，看整个团队的同级别用户）
        calculateStakePeerLevelRewards();
        
        log.info("质押动态收益计算完成");
    }

    /**
     * 计算锁仓动态收益（分别记录流水）
     */
    private void calculateLockOrderDynamicRewards() {
        log.info("开始计算锁仓动态收益");
        
        // 分享奖励
        calculateLockOrderShareRewards();
        
        // 社区奖励（含烧伤机制，按照Job3的实现逻辑）
        calculateLockOrderCommunityRewards();
        
        // 平级奖励（V4-V6，看整个团队的同级别用户）
        calculateLockOrderPeerLevelRewards();
        
        log.info("锁仓动态收益计算完成");
    }

    /**
     * 计算社区业绩（团队锁仓 USD 总金额）
     */
    public BigDecimal getCommunityLockUsdPerformance(Long userId) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null || userRelation.getPath() == null) {
            log.debug("用户{}没有用户关系记录", userId);
            return BigDecimal.ZERO;
        }

        // 查询所有下级用户
        // 路径格式：/0/2001/2002 表示2002是2001的下级
        String pathPrefix = userRelation.getPath() + "/" + userId + "/";
        List<UserRelation> teamUsers = userRelationMapper.selectList(
            new LambdaQueryWrapper<UserRelation>()
                .like(UserRelation::getPath, pathPrefix + "%")
        );

        log.debug("用户{}的团队用户数量: {}", userId, teamUsers.size());

        if (teamUsers.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<Long> teamUserIds = teamUsers.stream().map(UserRelation::getId).collect(Collectors.toList());
        if (teamUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 将用户ID列表转换为逗号分隔的字符串
        String userIdsStr = teamUserIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        
        // 查询团队用户的锁仓 USD 总金额
        BigDecimal totalUsdPerformance = lockOrderMapper.getUserListTotalUsdAmount(userIdsStr);

        log.debug("用户{}的社区锁仓USD业绩: {}", userId, totalUsdPerformance);
        return totalUsdPerformance;
    }

    /**
     * 计算社区业绩（团队总质押）- 保留原方法用于其他地方
     */
    public BigDecimal getCommunityPerformance(Long userId) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null || userRelation.getPath() == null) {
            log.debug("用户{}没有用户关系记录", userId);
            return BigDecimal.ZERO;
        }

        // 查询所有下级用户的质押总和
        // 路径格式：/0/2001/2002 表示2002是2001的下级
        String pathPrefix = userRelation.getPath() + "/" + userId + "/";
        List<UserRelation> teamUsers = userRelationMapper.selectList(
            new LambdaQueryWrapper<UserRelation>()
                .like(UserRelation::getPath, pathPrefix + "%")
        );

        log.debug("用户{}的团队用户数量: {}", userId, teamUsers.size());

        if (teamUsers.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<Long> teamUserIds = teamUsers.stream().map(UserRelation::getId).collect(Collectors.toList());
        if (teamUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<StakeUser> teamStakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, teamUserIds)
        );

        BigDecimal totalPerformance = teamStakeUsers.stream()
            .map(user -> Optional.ofNullable(user.getCurrentAmount()).orElse(BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.debug("用户{}的社区业绩: {}", userId, totalPerformance);
        return totalPerformance;
    }

    /**
     * 根据个人锁仓USD金额、社区锁仓USD业绩和团队要求确定用户等级
     */
    public int getUserLevel(BigDecimal personalLockUsd, BigDecimal communityPerf, Long userId) {
        log.debug("计算用户{}等级：个人锁仓USD={}, 社区锁仓USD业绩={}", userId, personalLockUsd, communityPerf);
        
        // V6: 个人100000U + 社区300W + 3个V5
        if (personalLockUsd.compareTo(new BigDecimal("100000")) >= 0 &&
            communityPerf.compareTo(new BigDecimal("3000000")) >= 0 &&
            getTeamLevelCount(userId, 5) >= 3) {
            log.debug("用户{}满足V6条件", userId);
            return 6;
        }
        // V5: 个人50000U + 社区100W + 3个V4
        else if (personalLockUsd.compareTo(new BigDecimal("50000")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("1000000")) >= 0 &&
                 getTeamLevelCount(userId, 4) >= 3) {
            log.debug("用户{}满足V5条件", userId);
            return 5;
        }
        // V4: 个人20000U + 社区30W + 3个V3
        else if (personalLockUsd.compareTo(new BigDecimal("20000")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("300000")) >= 0 &&
                 getTeamLevelCount(userId, 3) >= 3) {
            log.debug("用户{}满足V4条件", userId);
            return 4;
        }
        // V3: 个人5000U + 社区10W + 2个V2
        else if (personalLockUsd.compareTo(new BigDecimal("5000")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("100000")) >= 0 &&
                 getTeamLevelCount(userId, 2) >= 2) {
            log.debug("用户{}满足V3条件", userId);
            return 3;
        }
        // V2: 个人1000U + 社区3W + 2个V1
        else if (personalLockUsd.compareTo(new BigDecimal("1000")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("30000")) >= 0) {
            int v1Count = getTeamLevelCount(userId, 1);
            log.debug("用户{}检查V2条件：个人锁仓USD{}>=1000? {}, 社区锁仓USD业绩{}>=30000? {}, V1直推用户{}>=2? {}", 
                userId, personalLockUsd, personalLockUsd.compareTo(new BigDecimal("1000")) >= 0,
                communityPerf, communityPerf.compareTo(new BigDecimal("30000")) >= 0,
                v1Count, v1Count >= 2);
            if (v1Count >= 2) {
                log.debug("用户{}满足V2条件", userId);
                return 2;
            }
        }
        // V1: 个人100U + 社区1W
        else if (personalLockUsd.compareTo(new BigDecimal("100")) >= 0 &&
                 communityPerf.compareTo(new BigDecimal("10000")) >= 0) {
            log.debug("用户{}满足V1条件", userId);
            return 1;
        }
        
        log.debug("用户{}保持V0等级", userId);
        return 0;
    }

    /**
     * 批量更新用户等级
     */
    private void batchUpdateUserLevels(List<UserLevelUpdate> levelUpdates) {
        for (UserLevelUpdate update : levelUpdates) {
            userMapper.update(null,
                new LambdaUpdateWrapper<User>()
                    .eq(User::getId, update.getUserId())
                    .set(User::getLevel, update.getNewLevel())
            );
            
            log.debug("更新用户{}等级为V{}", update.getUserId(), update.getNewLevel());
        }
    }

    /**
     * 处理锁仓订单线性释放
     */
    private void processLinearRelease(LockOrder lockOrder, java.sql.Date today) {
        // 检查是否需要释放
        if (lockOrder.getReleasedDays() >= lockOrder.getTotalDays()) {
            return;
        }

        // 检查是否已经释放完成
        BigDecimal totalReleased = Optional.ofNullable(lockOrder.getReleasedAmount()).orElse(BigDecimal.ZERO);
        BigDecimal orderAmount = lockOrder.getLockAmount();
        if (totalReleased.compareTo(orderAmount) >= 0) {
            // 已经释放完成，更新订单状态为已完成
            if (lockOrder.getStatus() != 2) {
                lockOrderService.update(
                    new LambdaUpdateWrapper<LockOrder>()
                        .eq(LockOrder::getId, lockOrder.getId())
                        .set(LockOrder::getStatus, 2)  // 2-已完成
                );
                log.info("锁仓订单释放完成，订单号={}", lockOrder.getOrderNo());
            }
            return;
        }

        // 计算今日应释放金额
        BigDecimal dailyRelease = Optional.ofNullable(lockOrder.getDailyReleaseAmount()).orElse(BigDecimal.ZERO);
        if (dailyRelease.compareTo(BigDecimal.ZERO) <= 0) {
            // 如果每日释放金额为0，重新计算
            dailyRelease = orderAmount.divide(new BigDecimal(lockOrder.getTotalDays()), 8, BigDecimal.ROUND_HALF_UP);
        }

        // 确保不超过总金额
        BigDecimal remainingAmount = orderAmount.subtract(totalReleased);
        BigDecimal actualRelease = dailyRelease.min(remainingAmount);

        if (actualRelease.compareTo(BigDecimal.ZERO) > 0) {
            // 更新释放相关字段
            lockOrderService.update(
                new LambdaUpdateWrapper<LockOrder>()
                    .eq(LockOrder::getId, lockOrder.getId())
                    .set(LockOrder::getReleasedAmount, totalReleased.add(actualRelease))
                    .set(LockOrder::getAvailableAmount,
                        Optional.ofNullable(lockOrder.getAvailableAmount()).orElse(BigDecimal.ZERO).add(actualRelease))
                    .set(LockOrder::getDailyReleaseAmount, dailyRelease)
                    .set(LockOrder::getReleasedDays, lockOrder.getReleasedDays() + 1)
            );

            log.info("锁仓订单线性释放，订单号={}, 释放金额={}, 累计释放={}",
                lockOrder.getOrderNo(), actualRelease, totalReleased.add(actualRelease));
        }
    }

    /**
     * 获取或创建用户的StakeUser记录
     */
    private StakeUser getOrCreateStakeUser(Long userId) {
        StakeUser stakeUser = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>()
                .eq(StakeUser::getUserId, userId)
        );

        if (stakeUser == null) {
            // 如果用户没有StakeUser记录，创建一个
            stakeUser = new StakeUser();
            stakeUser.setUserId(userId);
            stakeUser.setTokenId("XYC"); // 默认tokenId
            stakeUser.setCurrentAmount(BigDecimal.ZERO);
            stakeUser.setPendingAmount(BigDecimal.ZERO);
            stakeUser.setStaticPool(BigDecimal.ZERO);
            stakeUser.setDynamicPool(BigDecimal.ZERO);
            stakeUser.setLockStaticPool(BigDecimal.ZERO);
            stakeUser.setLockDynamicPool(BigDecimal.ZERO);
            stakeUser.setTodayStatic(BigDecimal.ZERO);
            stakeUser.setTotalStatic(BigDecimal.ZERO);
            stakeUser.setTodayDynamic(BigDecimal.ZERO);
            stakeUser.setTotalDynamic(BigDecimal.ZERO);
            stakeUser.setCreateTime(new Date());
            stakeUserMapper.insert(stakeUser);
        }

        return stakeUser;
    }

    /**
     * 更新全网质押量 - 每次结算后执行，独立事务
     */
    private void updateTotalAmount() {
        log.info("开始更新全网质押量");
        
        try {
            String redisKey = "xyc:stats";
            String totalAmountField = "totalAmount";
            
            // 1. 获取质押本金总额
            BigDecimal totalStakeAmount = stakeUserMapper.getTotalStakeAmount("XYC");
            if (totalStakeAmount == null) {
                totalStakeAmount = BigDecimal.ZERO;
            }
            
            // 2. 获取锁仓总额
            BigDecimal totalLockAmount = lockOrderMapper.getTotalLockAmount("XYC");
            if (totalLockAmount == null) {
                totalLockAmount = BigDecimal.ZERO;
            }
            
            // 3. 获取XYC价格
            BigDecimal xycPrice = productService.getPrice("XYC");
            if (xycPrice == null) {
                xycPrice = BigDecimal.ZERO;
            }
            
            // 4. 计算全网总质押量 = (质押本金 + 锁仓总额) * XYC价格
            BigDecimal totalAmount = totalStakeAmount.add(totalLockAmount).multiply(xycPrice);
            
            // 5. 更新Redis中的全网总质押量
            stringRedisTemplate.opsForHash().put(redisKey, totalAmountField, totalAmount.toPlainString());
            
            log.info("全网质押量更新成功: 质押本金={}, 锁仓总额={}, XYC价格={}, 总质押量={}",
                totalStakeAmount.toPlainString(),
                totalLockAmount.toPlainString(), 
                xycPrice.toPlainString(),
                totalAmount.toPlainString());
                
        } catch (Exception e) {
            log.error("更新全网质押量失败", e);
            // 不抛异常，不影响主要结算逻辑
        }
    }

    /**
     * 更新当前指数 - 只在晚上8点执行，独立事务
     */
    private void updateCurrentIndex() {
        log.info("开始更新当前指数");
        
        try {
            String redisKey = "xyc:stats";
            String indexField = "currentIndex";
            
            // 获取当前指数和收益率，计算新指数
            Object currentIndexObj = stringRedisTemplate.opsForHash().get(redisKey, indexField);
            if (currentIndexObj == null) {
                log.warn("Redis中没有当前指数，跳过更新");
                return;
            }
            
            BigDecimal currentIndex = new BigDecimal(currentIndexObj.toString());
            SysConfig sysConfig = sysConfigService.getSysConfig();
            BigDecimal rewardRate = sysConfig.getRewardRate();
            
            if (rewardRate == null || rewardRate.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("收益率配置异常，跳过指数更新");
                return;
            }
            
            // 新指数 = 当前指数 * (1 + 静态收益率)
            BigDecimal newIndex = currentIndex.multiply(BigDecimal.ONE.add(rewardRate))
                .setScale(8, RoundingMode.HALF_UP);
            
            // 更新Redis
            stringRedisTemplate.opsForHash().put(redisKey, indexField, newIndex.toPlainString());
            
            log.info("指数更新成功: {} -> {}, 收益率: {}",
                currentIndex.toPlainString(),
                newIndex.toPlainString(),
                rewardRate.toPlainString());
                
        } catch (Exception e) {
            log.error("更新当前指数失败", e);
            // 不抛异常，不影响主要结算逻辑
        }
    }

    // 辅助方法
    private int getTeamLevelCount(Long userId, int targetLevel) {
        // 获取直推用户
        List<Long> directUserIds = getDirectUserIds(userId);
        if (directUserIds.isEmpty()) {
            log.debug("用户{}没有直推用户", userId);
            return 0;
        }

        log.debug("用户{}有{}个直推用户: {}", userId, directUserIds.size(), directUserIds);

        int count = 0;
        for (Long directUserId : directUserIds) {
            // 直接使用用户当前等级
            User directUser = userMapper.selectById(directUserId);
            if (directUser != null && directUser.getLevel() >= targetLevel) {
                count++;
                log.debug("直推用户{}当前等级V{}满足V{}要求", directUserId, directUser.getLevel(), targetLevel);
            }
        }

        log.debug("用户{}满足V{}要求的直推用户数量: {}", userId, targetLevel, count);
        return count;
    }

    public List<Long> getDirectUserIds(Long userId) {
        List<UserRelation> directUsers = userRelationMapper.selectList(
            new LambdaQueryWrapper<UserRelation>()
                .eq(UserRelation::getPid, userId)
        );
        return directUsers.stream().map(UserRelation::getId).collect(Collectors.toList());
    }

    /**
     * 计算质押分享奖励
     */
    private void calculateStakeShareRewards() {
        log.info("开始计算质押分享奖励");
        
        // 查询所有有质押的用户
        List<StakeUser> stakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getCurrentAmount, BigDecimal.ZERO)
        );
        
        for (StakeUser stakeUser : stakeUsers) {
            BigDecimal personalStake = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
            Long userId = stakeUser.getUserId();

            // 获取直推有效用户数量（质押100及以上）
            int validDirectUsers = getValidDirectUsers(userId);

            // 根据个人质押数量和直推有效用户数量确定奖励比例
            BigDecimal shareRewardRate = getShareRewardRate(personalStake, validDirectUsers);

            if (shareRewardRate.compareTo(BigDecimal.ZERO) > 0) {
                // 计算直推用户今日静态收益总和
                BigDecimal directStaticTotal = getDirectUsersTodayStatic(userId);

                if (directStaticTotal.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal shareReward = directStaticTotal.multiply(shareRewardRate)
                        .setScale(8, RoundingMode.HALF_UP);

                    BigDecimal dynamicPool = Optional.ofNullable(stakeUser.getDynamicPool()).orElse(BigDecimal.ZERO);
                    BigDecimal todayDynamic = Optional.ofNullable(stakeUser.getTodayDynamic()).orElse(BigDecimal.ZERO);
                    BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                    // 更新动态池子
                    stakeUserMapper.update(null,
                        new LambdaUpdateWrapper<StakeUser>()
                            .eq(StakeUser::getId, stakeUser.getId())
                            .set(StakeUser::getDynamicPool, dynamicPool.add(shareReward))
                            .set(StakeUser::getTodayDynamic, todayDynamic.add(shareReward))
                            .set(StakeUser::getTotalDynamic, totalDynamic.add(shareReward))
                    );
                    
                    // 记录质押分享奖励流水
                    userLogService.addLog(userId,
                        UserLogType.STAKE_SHARE_REWARD.getValue(),
                        shareReward,
                        "质押分享奖励");
                    
                    log.debug("用户{}质押分享奖励：直推静态={}, 奖励比例={}%, 奖励={}", 
                        userId, directStaticTotal, shareRewardRate.multiply(new BigDecimal("100")), shareReward);
                }
            }
        }
        
        log.info("质押分享奖励计算完成");
    }

    /**
     * 计算质押社区奖励
     */
    private void calculateStakeCommunityRewards() {
        log.info("开始计算质押社区奖励");

        // 查询所有有质押的用户
        List<StakeUser> stakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getCurrentAmount, BigDecimal.ZERO)
        );

        for (StakeUser stakeUser : stakeUsers) {
            Long userId = stakeUser.getUserId();
            BigDecimal personalStake = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);

            // 计算社区业绩（团队总质押）
            BigDecimal communityPerf = getCommunityPerformance(userId);

            // 确定用户级别
            int userLevel = getUserLevel(personalStake, communityPerf, userId);

            if (userLevel > 0) {
                // 获取社区奖励比例
                BigDecimal communityRewardRate = getCommunityRewardRate(userLevel);

                if (communityRewardRate.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算团队今日静态收益总和
                    BigDecimal teamStaticTotal = getTeamTodayStatic(userId);

                    if (teamStaticTotal.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal communityReward = teamStaticTotal.multiply(communityRewardRate)
                            .setScale(8, RoundingMode.HALF_UP);

                        // 烧伤机制：检查上级是否有更高级别
                        communityReward = applyBurnMechanism(userId, userLevel, communityReward);

                        if (communityReward.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal dynamicPool = Optional.ofNullable(stakeUser.getDynamicPool()).orElse(BigDecimal.ZERO);
                            BigDecimal todayDynamic = Optional.ofNullable(stakeUser.getTodayDynamic()).orElse(BigDecimal.ZERO);
                            BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                            // 更新动态池子
                            stakeUserMapper.update(null,
                                new LambdaUpdateWrapper<StakeUser>()
                                    .eq(StakeUser::getId, stakeUser.getId())
                                    .set(StakeUser::getDynamicPool, dynamicPool.add(communityReward))
                                    .set(StakeUser::getTodayDynamic, todayDynamic.add(communityReward))
                                    .set(StakeUser::getTotalDynamic, totalDynamic.add(communityReward))
                            );

                            // 记录质押社区奖励流水
                            userLogService.addLog(userId,
                                UserLogType.STAKE_COMMUNITY_REWARD.getValue(),
                                communityReward,
                                "质押社区奖励V" + userLevel);

                            log.debug("用户{}质押社区奖励：级别=V{}, 团队静态={}, 奖励比例={}%, 奖励={}",
                                userId, userLevel, teamStaticTotal,
                                communityRewardRate.multiply(new BigDecimal("100")), communityReward);
                        }
                    }
                }
            }
        }

        log.info("质押社区奖励计算完成");
    }

    /**
     * 计算质押平级奖励（V4-V6有5%平级奖励）
     */
    private void calculateStakePeerLevelRewards() {
        log.info("开始计算质押平级奖励");

        // 查询所有V4及以上级别的用户
        List<StakeUser> stakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .gt(StakeUser::getCurrentAmount, BigDecimal.ZERO)
        );

        for (StakeUser stakeUser : stakeUsers) {
            Long userId = stakeUser.getUserId();
            BigDecimal personalStake = Optional.ofNullable(stakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);

            // 计算社区业绩
            BigDecimal communityPerf = getCommunityPerformance(userId);

            // 确定用户级别
            int userLevel = getUserLevel(personalStake, communityPerf, userId);

            // 只有V4-V6级别才有平级奖励
            if (userLevel >= 4 && userLevel <= 6) {
                // 计算平级奖励：整个团队同级别用户的静态收益 * 5%
                BigDecimal peerReward = calculatePeerLevelReward(userId, userLevel);

                if (peerReward.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal dynamicPool = Optional.ofNullable(stakeUser.getDynamicPool()).orElse(BigDecimal.ZERO);
                    BigDecimal todayDynamic = Optional.ofNullable(stakeUser.getTodayDynamic()).orElse(BigDecimal.ZERO);
                    BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                    // 更新动态池子
                    stakeUserMapper.update(null,
                        new LambdaUpdateWrapper<StakeUser>()
                            .eq(StakeUser::getId, stakeUser.getId())
                            .set(StakeUser::getDynamicPool, dynamicPool.add(peerReward))
                            .set(StakeUser::getTodayDynamic, todayDynamic.add(peerReward))
                            .set(StakeUser::getTotalDynamic, totalDynamic.add(peerReward))
                    );

                    // 记录质押平级奖励流水
                    userLogService.addLog(userId,
                        UserLogType.STAKE_PEER_LEVEL_REWARD.getValue(),
                        peerReward,
                        "质押平级奖励V" + userLevel);

                    log.debug("用户{}质押平级奖励：级别=V{}, 奖励={}",
                        userId, userLevel, peerReward);
                }
            }
        }

        log.info("质押平级奖励计算完成");
    }

    /**
     * 计算锁仓分享奖励
     */
    private void calculateLockOrderShareRewards() {
        log.info("开始计算锁仓分享奖励");
        
        // 查询所有有锁仓订单的用户
        List<LockOrder> activeLockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1)  // status=1 锁仓中
        );

        // 按用户分组
        Map<Long, List<LockOrder>> userLockOrders = activeLockOrders.stream()
            .collect(Collectors.groupingBy(LockOrder::getUserId));

        for (Map.Entry<Long, List<LockOrder>> entry : userLockOrders.entrySet()) {
            Long userId = entry.getKey();
            
            // 计算用户锁仓总金额作为个人质押
            BigDecimal personalLockAmount = entry.getValue().stream()
                .map(order -> Optional.ofNullable(order.getLockAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 获取直推有效用户数量（质押100及以上）
            int validDirectUsers = getValidDirectUsers(userId);

            // 根据个人锁仓数量和直推有效用户数量确定奖励比例
            BigDecimal shareRewardRate = getShareRewardRate(personalLockAmount, validDirectUsers);

            if (shareRewardRate.compareTo(BigDecimal.ZERO) > 0) {
                // 计算直推用户今日静态收益总和（包括锁仓静态收益）
                BigDecimal directStaticTotal = getDirectUsersTodayStatic(userId);

                if (directStaticTotal.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal shareReward = directStaticTotal.multiply(shareRewardRate)
                        .setScale(8, RoundingMode.HALF_UP);

                    // 获取或创建用户的StakeUser记录
                    StakeUser stakeUser = getOrCreateStakeUser(userId);
                    
                    BigDecimal lockDynamicPool = Optional.ofNullable(stakeUser.getLockDynamicPool()).orElse(BigDecimal.ZERO);
                    BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                    // 更新锁仓动态池子
                    stakeUserMapper.update(null,
                        new LambdaUpdateWrapper<StakeUser>()
                            .eq(StakeUser::getId, stakeUser.getId())
                            .set(StakeUser::getLockDynamicPool, lockDynamicPool.add(shareReward))
                            .set(StakeUser::getTotalDynamic, totalDynamic.add(shareReward))
                    );
                    
                    // 记录锁仓分享奖励流水
                    userLogService.addLog(userId,
                        UserLogType.STAKE_SHARE_REWARD.getValue(),
                        shareReward,
                        "锁仓分享奖励");
                    
                    log.debug("用户{}锁仓分享奖励：直推静态={}, 奖励比例={}%, 奖励={}", 
                        userId, directStaticTotal, shareRewardRate.multiply(new BigDecimal("100")), shareReward);
                }
            }
        }
        
        log.info("锁仓分享奖励计算完成");
    }

    /**
     * 计算锁仓社区奖励
     */
    private void calculateLockOrderCommunityRewards() {
        log.info("开始计算锁仓社区奖励");

        // 查询所有有锁仓订单的用户
        List<LockOrder> activeLockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1)  // status=1 锁仓中
        );

        // 按用户分组
        Map<Long, List<LockOrder>> userLockOrders = activeLockOrders.stream()
            .collect(Collectors.groupingBy(LockOrder::getUserId));

        for (Map.Entry<Long, List<LockOrder>> entry : userLockOrders.entrySet()) {
            Long userId = entry.getKey();
            
            // 计算用户锁仓总金额作为个人质押
            BigDecimal personalLockAmount = entry.getValue().stream()
                .map(order -> Optional.ofNullable(order.getLockAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算社区业绩（团队总质押）
            BigDecimal communityPerf = getCommunityPerformance(userId);

            // 确定用户级别
            int userLevel = getUserLevel(personalLockAmount, communityPerf, userId);

            if (userLevel > 0) {
                // 获取社区奖励比例
                BigDecimal communityRewardRate = getCommunityRewardRate(userLevel);

                if (communityRewardRate.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算团队今日静态收益总和
                    BigDecimal teamStaticTotal = getTeamTodayStatic(userId);

                    if (teamStaticTotal.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal communityReward = teamStaticTotal.multiply(communityRewardRate)
                            .setScale(8, RoundingMode.HALF_UP);

                        // 烧伤机制：检查上级是否有更高级别
                        communityReward = applyBurnMechanism(userId, userLevel, communityReward);

                        if (communityReward.compareTo(BigDecimal.ZERO) > 0) {
                            // 获取或创建用户的StakeUser记录
                            StakeUser stakeUser = getOrCreateStakeUser(userId);
                            
                            BigDecimal lockDynamicPool = Optional.ofNullable(stakeUser.getLockDynamicPool()).orElse(BigDecimal.ZERO);
                            BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                            // 更新锁仓动态池子
                            stakeUserMapper.update(null,
                                new LambdaUpdateWrapper<StakeUser>()
                                    .eq(StakeUser::getId, stakeUser.getId())
                                    .set(StakeUser::getLockDynamicPool, lockDynamicPool.add(communityReward))
                                    .set(StakeUser::getTotalDynamic, totalDynamic.add(communityReward))
                            );

                            // 记录锁仓社区奖励流水
                            userLogService.addLog(userId,
                                UserLogType.STAKE_COMMUNITY_REWARD.getValue(),
                                communityReward,
                                "锁仓社区奖励V" + userLevel);

                            log.debug("用户{}锁仓社区奖励：级别=V{}, 团队静态={}, 奖励比例={}%, 奖励={}",
                                userId, userLevel, teamStaticTotal,
                                communityRewardRate.multiply(new BigDecimal("100")), communityReward);
                        }
                    }
                }
            }
        }

        log.info("锁仓社区奖励计算完成");
    }

    /**
     * 计算锁仓平级奖励（V4-V6有5%平级奖励）
     */
    private void calculateLockOrderPeerLevelRewards() {
        log.info("开始计算锁仓平级奖励");

        // 查询所有有锁仓订单的用户
        List<LockOrder> activeLockOrders = lockOrderService.list(
            new LambdaQueryWrapper<LockOrder>()
                .eq(LockOrder::getStatus, 1)  // status=1 锁仓中
        );

        // 按用户分组
        Map<Long, List<LockOrder>> userLockOrders = activeLockOrders.stream()
            .collect(Collectors.groupingBy(LockOrder::getUserId));

        for (Map.Entry<Long, List<LockOrder>> entry : userLockOrders.entrySet()) {
            Long userId = entry.getKey();
            
            // 计算用户锁仓总金额作为个人质押
            BigDecimal personalLockAmount = entry.getValue().stream()
                .map(order -> Optional.ofNullable(order.getLockAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算社区业绩
            BigDecimal communityPerf = getCommunityPerformance(userId);

            // 确定用户级别
            int userLevel = getUserLevel(personalLockAmount, communityPerf, userId);

            // 只有V4-V6级别才有平级奖励
            if (userLevel >= 4 && userLevel <= 6) {
                // 计算平级奖励：整个团队同级别用户的静态收益 * 5%
                BigDecimal peerReward = calculatePeerLevelReward(userId, userLevel);

                if (peerReward.compareTo(BigDecimal.ZERO) > 0) {
                    // 获取或创建用户的StakeUser记录
                    StakeUser stakeUser = getOrCreateStakeUser(userId);
                    
                    BigDecimal lockDynamicPool = Optional.ofNullable(stakeUser.getLockDynamicPool()).orElse(BigDecimal.ZERO);
                    BigDecimal totalDynamic = Optional.ofNullable(stakeUser.getTotalDynamic()).orElse(BigDecimal.ZERO);

                    // 更新锁仓动态池子
                    stakeUserMapper.update(null,
                        new LambdaUpdateWrapper<StakeUser>()
                            .eq(StakeUser::getId, stakeUser.getId())
                            .set(StakeUser::getLockDynamicPool, lockDynamicPool.add(peerReward))
                            .set(StakeUser::getTotalDynamic, totalDynamic.add(peerReward))
                    );

                    // 记录锁仓平级奖励流水
                    userLogService.addLog(userId,
                        UserLogType.STAKE_PEER_LEVEL_REWARD.getValue(),
                        peerReward,
                        "锁仓平级奖励V" + userLevel);

                    log.debug("用户{}锁仓平级奖励：级别=V{}, 奖励={}",
                        userId, userLevel, peerReward);
                }
            }
        }

        log.info("锁仓平级奖励计算完成");
    }

    // 辅助方法
    private int getValidDirectUsers(Long userId) {
        List<Long> directUserIds = getDirectUserIds(userId);
        if (directUserIds.isEmpty()) {
            return 0;
        }
        return stakeUserMapper.selectCount(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, directUserIds)
                .ge(StakeUser::getCurrentAmount, new BigDecimal("100"))
        ).intValue();
    }

    private BigDecimal getShareRewardRate(BigDecimal personalStake, int validDirectUsers) {
        if (personalStake.compareTo(new BigDecimal("2000")) >= 0 && validDirectUsers >= 10) {
            return new BigDecimal("0.02"); // 2%
        } else if (personalStake.compareTo(new BigDecimal("1000")) >= 0 && validDirectUsers >= 6) {
            return new BigDecimal("0.05"); // 5%
        } else if (personalStake.compareTo(new BigDecimal("300")) >= 0 && validDirectUsers >= 3) {
            return new BigDecimal("0.09"); // 9%
        } else if (personalStake.compareTo(new BigDecimal("100")) >= 0 && validDirectUsers >= 1) {
            return new BigDecimal("0.12"); // 12%
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal getDirectUsersTodayStatic(Long userId) {
        List<Long> directUserIds = getDirectUserIds(userId);
        if (directUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<StakeUser> directStakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, directUserIds)
        );

        return directStakeUsers.stream()
            .map(user -> Optional.ofNullable(user.getTodayStatic()).orElse(BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getCommunityRewardRate(int level) {
        switch (level) {
            case 6: return new BigDecimal("0.70"); // 70%
            case 5: return new BigDecimal("0.60"); // 60%
            case 4: return new BigDecimal("0.50"); // 50%
            case 3: return new BigDecimal("0.35"); // 35%
            case 2: return new BigDecimal("0.20"); // 20%
            case 1: return new BigDecimal("0.10"); // 10%
            default: return BigDecimal.ZERO;
        }
    }

    private BigDecimal getTeamTodayStatic(Long userId) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null || userRelation.getPath() == null) {
            return BigDecimal.ZERO;
        }

        // 查询所有下级用户的今日静态收益总和
        List<UserRelation> teamUsers = userRelationMapper.selectList(
            new LambdaQueryWrapper<UserRelation>()
                .like(UserRelation::getPath, userRelation.getPath() + "/" + userId + "/")
        );

        if (teamUsers.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<Long> teamUserIds = teamUsers.stream().map(UserRelation::getId).collect(Collectors.toList());
        if (teamUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<StakeUser> teamStakeUsers = stakeUserMapper.selectList(
            new LambdaQueryWrapper<StakeUser>()
                .in(StakeUser::getUserId, teamUserIds)
        );

        return teamStakeUsers.stream()
            .map(user -> Optional.ofNullable(user.getTodayStatic()).orElse(BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal applyBurnMechanism(Long userId, int userLevel, BigDecimal reward) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null || userRelation.getPid() == null) {
            return reward;
        }

        // 检查上级用户级别
        Long parentId = userRelation.getPid();
        StakeUser parentStakeUser = stakeUserMapper.selectOne(
            new LambdaQueryWrapper<StakeUser>()
                .eq(StakeUser::getUserId, parentId)
        );

        if (parentStakeUser != null) {
            BigDecimal parentPersonalStake = Optional.ofNullable(parentStakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
            BigDecimal parentCommunityPerf = getCommunityPerformance(parentId);
            int parentLevel = getUserLevel(parentPersonalStake, parentCommunityPerf, parentId);

            // 如果上级级别更高，则烧伤（减少奖励）
            if (parentLevel > userLevel) {
                // 烧伤比例可以根据级别差异调整，这里简单设为50%
                return reward.multiply(new BigDecimal("0.5"));
            }
        }

        return reward;
    }

    private BigDecimal calculatePeerLevelReward(Long userId, int userLevel) {
        // 获取整个团队用户
        List<Long> teamUserIds = getTeamUserIds(userId);
        if (teamUserIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalPeerStatic = BigDecimal.ZERO;

        for (Long teamUserId : teamUserIds) {
            StakeUser teamStakeUser = stakeUserMapper.selectOne(
                new LambdaQueryWrapper<StakeUser>()
                    .eq(StakeUser::getUserId, teamUserId)
            );
            
            if (teamStakeUser != null) {
                BigDecimal teamPersonalStake = Optional.ofNullable(teamStakeUser.getCurrentAmount()).orElse(BigDecimal.ZERO);
                BigDecimal teamCommunityPerf = getCommunityPerformance(teamUserId);
                int teamUserLevel = getUserLevel(teamPersonalStake, teamCommunityPerf, teamUserId);
                
                // 只计算同级别用户的静态收益
                if (teamUserLevel == userLevel) {
                    BigDecimal teamUserTodayStatic = Optional.ofNullable(teamStakeUser.getTodayStatic()).orElse(BigDecimal.ZERO);
                    totalPeerStatic = totalPeerStatic.add(teamUserTodayStatic);
                }
            }
        }

        // 平级奖励 = 同级别用户静态收益总和 * 5%
        return totalPeerStatic.multiply(new BigDecimal("0.05"))
            .setScale(8, RoundingMode.HALF_UP);
    }

    private List<Long> getTeamUserIds(Long userId) {
        UserRelation userRelation = userRelationMapper.selectById(userId);
        if (userRelation == null || userRelation.getPath() == null) {
            return new ArrayList<>();
        }

        // 查询所有下级用户
        List<UserRelation> teamUsers = userRelationMapper.selectList(
            new LambdaQueryWrapper<UserRelation>()
                .like(UserRelation::getPath, userRelation.getPath() + "/" + userId + "/")
        );

        return teamUsers.stream().map(UserRelation::getId).collect(Collectors.toList());
    }
}