package com.aic.app.job;

import com.aic.app.model.Asset;
import com.aic.app.model.Events;
import com.aic.app.service.*;
import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class BlockJob {

    @Resource
    IEventsService eventsService;

    @Value("${router.address}")
    private String routerAddress;

    @Value("${referral.address}")
    private String referralAddress;

   @PostConstruct
    public void init() {
        Thread thread = new Thread(this::run);
        thread.start();
    }
    
    public void run() {
        log.info("[job1] 同步区块任务开启");
        try {
            // 先处理一遍，看看有没有待处理的事件，然后再同步，防止上次处理失败，一直卡着
            syncEvents();
        } catch (Exception e) {
            log.error("[job1] 同步区块失败", e);
        }
        
        for (;;) {

            try {
                // 先处理一遍，看看有没有待处理的事件，然后再同步，防止上次处理失败，一直卡着
                if (Utils.isCloseTime()) {
                    sleep(1000 * 30);
                    continue;
                }
                syncEvents();
            } catch (Exception e) {
                log.error("[job1] 同步区块失败", e);
            }


            try {
                if (Utils.isCloseTime()) {
                    sleep(1000 * 30);
                    continue;
                }
                String idoAddress = "";
                // 购买节点
                String stakeAddress = routerAddress;
                // 邀请人
                String nodeAddress = referralAddress;
                String withdrawAddress = "";
                boolean result;
                do {
                    if (Utils.isCloseTime()) {
                        break;
                    }
                    result = RustService.syncBlock(idoAddress, stakeAddress, nodeAddress, withdrawAddress);
                    if (result) {
                        log.info("同步到事件");
                        if (Utils.isCloseTime()) {
                            break;
                        }
                        syncEvents();
                    }
                } while (result);
            } catch (Exception e) {
                log.error("[job1] 同步区块失败", e);
            }
            sleep(15000);
//            try {
//                Thread.sleep(15000);
//            } catch (InterruptedException e) {
//                log.error("[job1] 同步区块睡眠失败", e);
//            }
        }
    }
    
    public void syncEvents() {
        List<Events> eventsList = eventsService.list(new LambdaQueryWrapper<Events>().eq(Events::getStatus, 0));
        for (Events events : eventsList) {
            eventsService.handleEvent(events);
        }
    }
    
    // 睡眠
    public void sleep(long millis) {
        try {
//            log.info("睡眠15秒");
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            log.error("[job1] 同步区块睡眠失败", e);
        }
    }
}
