package com.aic.app.api;

import com.aic.app.exception.Errors;
import com.aic.app.form.AddressLoginForm;
import com.aic.app.form.ProductQuery;
import com.aic.app.model.AssetEnum;
import com.aic.app.model.Chart;
import com.aic.app.model.Product;
import com.aic.app.model.User;
import com.aic.app.model.StakeUser;
import com.aic.app.service.*;
import com.aic.app.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@AllArgsConstructor
@Slf4j
public class IndexController {

    IStakeUserService stakeUserService;
    StringRedisTemplate stringRedisTemplate;
    IUserService userService;
    IProductService productService;
    IChartService chartService;

    @GetMapping
    public Object index() {
        return "it work!!";
    }

    @GetMapping("/api/version")
    public Object version() {
        return "20250728";
    }

    // /api/address/login?address=&sign=
    @Operation(summary = "地址登录")
    @PostMapping("/api/address/login")
    public Result<LoginResult> addressLogin(@RequestBody AddressLoginForm form) {
        String address = form.getAddress();
        String sign = form.getSign();
        Boolean ok = RustService.checkSignMsg(address, sign);
        if (!Boolean.TRUE.equals(ok)) {
            throw Errors.LOGIN_EXCEPTION;
        }
        User phoneUser = stakeUserService.checkUser(address);
        String token = UUID.randomUUID().toString();
        stringRedisTemplate.opsForValue().set("xyc:user:token:" + token, phoneUser.getId().toString(), Duration.ofHours(2));
        stringRedisTemplate.opsForHash().putAll("xyc:user:" + phoneUser.getId(), 
                Map.of("token", token));
        stringRedisTemplate.expire("xyc:user:" + phoneUser.getId(), Duration.ofDays(30));
        log.info("用户登录成功 uid = {}", phoneUser.getId());
        return Result.success(new LoginResult(phoneUser, token));
    }

    @Operation(summary = "获取签名消息")
    @GetMapping("/api/sign-msg")
    public Result<String> signMsg(@RequestParam("address") String address) {
        String msg = RustService.getSignMsg(address);
        return Result.success(msg);
    }

//    @Operation(summary = "检查地址是否存在")
//    @GetMapping("/api/check-address")
//    public Result<Boolean> checkAddress(@RequestParam("address") String address) {
//        long count = userService.count(new LambdaQueryWrapper<User>().eq(User::getAddress, address));
//        return Result.success(count > 0);
//    }

    @GetMapping("/api/userinfo")
    @Operation(summary = "用户信息")
    public Result<UserInfoVo> userinfo(@RequestAttribute(value = "user", required = false) User user) {
        UserInfoVo vo = new UserInfoVo(user);
        return Result.success(vo);
    }
    

    @GetMapping("/api/product")
    @Operation(summary = "产品列表")
    public Result<IPage<ProductVo>> product(@ParameterObject ProductQuery query) {
        QueryWrapper<Product> qw = new QueryWrapper<Product>()
                .eq("enable", true)
                .in(query.getTypes() != null && !query.getTypes().isEmpty(), "type", query.getTypes())
                .like(StringUtils.isNotEmpty(query.getName()), "name", query.getName());

        // 动态排序
        if (StringUtils.isNotEmpty(query.getOrder())) {
            String sortField = query.getProp();
            // 验证排序字段，防止SQL注入
            if ("createTime".equals(sortField)) {
                sortField = "create_time";
            } else if ("price".equals(sortField)) {
                sortField = "price";
            } else {
                sortField = "id"; // 默认按ID排序
            }
            qw.orderBy(true, "ascending".equals(query.getOrder()), sortField);
        } else {
            qw.orderByDesc("id"); // 默认按ID降序
        }

        IPage<ProductVo> page = productService.page(new Page<>(query.getPage(), query.getSize()), qw)
                .convert(ProductVo::new);

        return Result.success(page);
    }

    @GetMapping("/api/stake")
    @Operation(summary = "我的质押接口")
    public Result<HomeDataVo> stake(@RequestAttribute(value = "user", required = false) User user) {
        // 1. 从Redis获取全网数据
        BigDecimal totalStakeAmount = getRedisHashValue("xyc:stats", "totalStakeAmount");
        BigDecimal currentIndex = getRedisHashValue("xyc:stats", "currentIndex");
        BigDecimal stakePrincipal = getRedisHashValue("xyc:stats", "stakePrincipal");
        BigDecimal totalInterest = getRedisHashValue("xyc:stats", "totalInterest");
        BigDecimal apy = getRedisHashValue("xyc:stats", "apy");

        // 2. 获取用户个人数据
        BigDecimal myStakeAmount = BigDecimal.ZERO;
        BigDecimal pendingAmount = BigDecimal.ZERO;
        BigDecimal stakeStaticPool = BigDecimal.ZERO;
        BigDecimal stakeDynamicPool = BigDecimal.ZERO;
        BigDecimal lockStaticPool = BigDecimal.ZERO;
        BigDecimal lockDynamicPool = BigDecimal.ZERO;

        if (user != null) {
            // 获取用户质押数据
            StakeUser stakeUser = stakeUserService.getStakeUser(user.getId(), AssetEnum.XYC.getTokenId());
            if (stakeUser != null) {
                myStakeAmount = stakeUser.getCurrentAmount() != null ? stakeUser.getCurrentAmount() : BigDecimal.ZERO;
                pendingAmount = stakeUser.getPendingAmount() != null ? stakeUser.getPendingAmount() : BigDecimal.ZERO;
                stakeStaticPool = stakeUser.getStaticPool() != null ? stakeUser.getStaticPool() : BigDecimal.ZERO;
                stakeDynamicPool = stakeUser.getDynamicPool() != null ? stakeUser.getDynamicPool() : BigDecimal.ZERO;
                lockStaticPool = stakeUser.getLockStaticPool() != null ? stakeUser.getLockStaticPool() : BigDecimal.ZERO;
                lockDynamicPool = stakeUser.getLockDynamicPool() != null ? stakeUser.getLockDynamicPool() : BigDecimal.ZERO;
            }
        }

        // 3. 构造返回数据
        HomeDataVo stakeData = new HomeDataVo(
            totalStakeAmount, currentIndex, stakePrincipal, totalInterest,
            myStakeAmount, pendingAmount, stakeStaticPool, stakeDynamicPool,
            lockStaticPool, lockDynamicPool, apy
        );

        return Result.success(stakeData);
    }

    /**
     * 从Redis Hash中获取BigDecimal值
     */
    private BigDecimal getRedisHashValue(String key, String field) {
        try {
            Object value = stringRedisTemplate.opsForHash().get(key, field);
            if (value != null) {
                return new BigDecimal(value.toString());
            }
        } catch (Exception e) {
            log.warn("从Redis获取数据失败，key: {}, field: {}, error: {}", key, field, e.getMessage());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 根据图表类型获取最新144条数据，按时间正序排序
     */
    private List<DashboardVo.ChartData> getChartDataByType(Integer type) {
        try {
            // 查询指定类型的最新144条数据，按时间倒序
            List<Chart> charts = chartService.list(
                new LambdaQueryWrapper<Chart>()
                    .eq(Chart::getType, type)
                    .orderByDesc(Chart::getTime)
                    .last("LIMIT 144")
            );

            // 转换为ChartData并按时间正序排序
            return charts.stream()
                    .map(DashboardVo.ChartData::new)
                    .sorted((a, b) -> Long.compare(a.getTime(), b.getTime()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取图表数据失败，type: {}", type, e);
            return new ArrayList<>();
        }
    }

    @GetMapping("/api/dashboard")
    @Operation(summary = "Dashboard数据")
    public Result<DashboardVo> dashboard(@RequestAttribute(value = "user", required = false) User user) {
        // 允许未登录用户访问，返回结构一致的数据
        if (user == null) {
            log.info("未登录用户查询Dashboard，返回默认数据");
        }

        try {
            DashboardVo dashboardData = new DashboardVo();

            // 1. 全网质押数量 - 从Redis获取totalStakeAmount
            BigDecimal totalStakeAmount = getRedisHashValue("xyc:stats", "totalStakeAmount");
            dashboardData.setTotalStakeAmount(totalStakeAmount);

            // 2. 获取基础数据用于计算
            BigDecimal totalSupply = getRedisHashValue("xyc:stats", "totalSupply");
            BigDecimal burnAmount = getRedisHashValue("xyc:stats", "burnAmount");
            BigDecimal xycPrice = getRedisHashValue("xyc:stats", "xycPrice");

            // 3. 全网未质押数量 = 总量 - 销毁 - 质押数量
            BigDecimal totalUnstakedAmount = totalSupply.subtract(burnAmount).subtract(totalStakeAmount);
            // 确保不为负数
            if (totalUnstakedAmount.compareTo(BigDecimal.ZERO) < 0) {
                totalUnstakedAmount = BigDecimal.ZERO;
            }
            dashboardData.setTotalUnstakedAmount(totalUnstakedAmount);

            // 4. LP价值 - 从Redis获取（暂时设为0，等待确认字段名）
            BigDecimal lpValue = getRedisHashValue("xyc:stats", "lpValue");
            dashboardData.setLpValue(lpValue);

            // 5. totalBondsSold - 统计产品表债券的已售数量
            BigDecimal totalBondsSold = productService.getSoldSumByType(3);
            dashboardData.setTotalBondsSold(totalBondsSold);

            // 6. 总市值 = (总量 - 销毁数量) × XYC价格
            BigDecimal totalMarketCap = totalSupply.subtract(burnAmount).multiply(xycPrice);
            dashboardData.setTotalMarketCap(totalMarketCap);

            // 7. totalSupply - 从Redis获取
            dashboardData.setTotalSupply(totalSupply);

            // 8. xycPrice - 从Redis获取
            dashboardData.setXycPrice(xycPrice);

            // 9. 查询图表数据，各取最后144条，按时间排序
            List<DashboardVo.ChartData> chartData1 = getChartDataByType(1);
            List<DashboardVo.ChartData> chartData2 = getChartDataByType(2);
            List<DashboardVo.ChartData> chartData3 = getChartDataByType(3);

            dashboardData.setChartData1(chartData1);
            dashboardData.setChartData2(chartData2);
            dashboardData.setChartData3(chartData3);

            log.info("Dashboard数据获取成功，totalStakeAmount: {}, totalUnstakedAmount: {}, totalMarketCap: {}, 图表数据: [{}, {}, {}]",
                    totalStakeAmount, totalUnstakedAmount, totalMarketCap,
                    chartData1.size(), chartData2.size(), chartData3.size());

            return Result.success(dashboardData);

        } catch (Exception e) {
            log.error("获取Dashboard数据失败", e);
            // 发生异常时返回默认数据，但仍尝试获取图表数据
            DashboardVo defaultData = DashboardVo.createDefault();
            try {
                defaultData.setChartData1(getChartDataByType(1));
                defaultData.setChartData2(getChartDataByType(2));
                defaultData.setChartData3(getChartDataByType(3));
            } catch (Exception chartException) {
                log.error("获取图表数据也失败", chartException);
            }
            return Result.success(defaultData);
        }
    }

    @GetMapping("/api/bonds")
    @Operation(summary = "债券接口")
    public Result<BondsResponseVo> bonds() {
        BondsResponseVo response = new BondsResponseVo();

        // 获取债券列表 (type=3)
        List<Product> bonds = productService.list(new LambdaQueryWrapper<Product>()
                .eq(Product::getType, 3)
                .eq(Product::getEnable, true)
                .orderByDesc(Product::getId));

        // 转换为ProductVo列表
        List<ProductVo> bondList = bonds.stream()
                .map(ProductVo::new)
                .collect(Collectors.toList());

        // 使用SQL SUM函数统计已售债券总数，不判断产品状态，直接统计，最小为0
        BigDecimal soldBonds = productService.getSoldSumByType(3);

        // 获取XYC价格
        BigDecimal xycPrice = productService.getPrice(AssetEnum.XYC.getTokenId());

        response.setSoldBonds(soldBonds);
        response.setXycPrice(xycPrice);
        response.setBondList(bondList);

        return Result.success(response);
    }

}
