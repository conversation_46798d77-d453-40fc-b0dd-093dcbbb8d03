package com.aic.app.api;

import com.aic.app.job.Job30;
import com.aic.app.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@AllArgsConstructor
@RestController
@Slf4j
public class JobController {

    Job30 job30;
    StringRedisTemplate stringRedisTemplate;
    private static final String LOCK_KEY_PREFIX = "xyc:job_lock:";

    @Operation(summary = "测试8点发收益")
    @GetMapping("/test/job8")
    public String test8(@RequestParam("pwd") String pwd) {
        if (!"admin888888".equals(pwd)) {
            return "err";
        }
        return checkAndLock(LOCK_KEY_PREFIX + "job1", () -> {
            job30.runMorning();
        });
    }

    @Operation(summary = "测试20点发收益")
    @GetMapping("/test/job20")
    public String test20(@RequestParam("pwd") String pwd) {
        if (!"admin888888".equals(pwd)) {
            return "err";
        }
        return checkAndLock(LOCK_KEY_PREFIX + "job1", () -> {
            job30.runEvening();
        });
    }


    private String checkAndLock(String lockKey, Runnable runnable) {
        String value = stringRedisTemplate.opsForValue().get(lockKey);
        log.info("获取锁结果 {}", value);
        if (StringUtils.isEmpty(value)) {
            runnable.run();
            return "任务执行成功";
        } else {
            return "任务正在其他实例上执行，请稍后再试";
        }
    }

}
