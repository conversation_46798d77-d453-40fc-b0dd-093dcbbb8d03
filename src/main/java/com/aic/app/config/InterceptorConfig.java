package com.aic.app.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.aic.app.admin.AdminInterceptor;
import com.aic.app.api.LoginInterceptor;

import jakarta.annotation.Resource;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Resource
    LoginInterceptor loginInterceptor;
    @Resource
    AdminInterceptor adminInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
//        WebMvcConfigurer.super.addInterceptors(registry);
        registry.addInterceptor(loginInterceptor).addPathPatterns("/api/**")
                .excludePathPatterns("/api/address/login")
                .excludePathPatterns("/api/sign-msg")
                .excludePathPatterns("/api/check-address")
                .excludePathPatterns("/api/login")
                .excludePathPatterns("/api/logout");
        registry.addInterceptor(adminInterceptor)
                .excludePathPatterns("/admin/login", "/admin/logout", "/admin/captchaImage")
                .addPathPatterns("/admin/**");
    }
}
